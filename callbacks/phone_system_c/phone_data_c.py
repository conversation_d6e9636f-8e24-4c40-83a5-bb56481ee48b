"""
电话号码数据管理页面回调函数
"""
import dash
from dash import Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime
import json

from server import app
from api.phone_data import (
    get_phone_data_list_api,
    add_phone_data_api,
    edit_phone_data_api,
    delete_phone_data_api
)


# 高性能分页查询回调
@app.callback(
    [
        Output('phone-data-table', 'data'),
        Output('phone-data-table', 'pagination'),
        Output('phone-data-table', 'loading')
    ],
    [
        Input('phone-data-search-btn', 'n_clicks'),
        Input('phone-data-table', 'pagination'),
        Input('phone-data-refresh-btn', 'n_clicks')
    ],
    [
        State('phone-data-phone-number-input', 'value'),
        State('phone-data-province-select', 'value'),
        State('phone-data-city-select', 'value'),
        State('phone-data-status-select', 'value'),
        State('phone-data-search-params', 'data')
    ],
    prevent_initial_call=True
)
def update_phone_data_table(search_clicks, pagination, refresh_clicks,
                           phone_number, province, city, status, search_params):
    """
    更新电话号码数据表格
    支持高性能分页查询
    """
    # 确定触发源
    trigger_id = ctx.triggered_id
    
    # 构建查询参数
    query_params = {}
    if phone_number:
        query_params['phone_number'] = phone_number
    if province:
        query_params['province'] = province
    if city:
        query_params['city'] = city
    if status:
        query_params['status'] = status
    
    # 分页参数
    if pagination:
        current_page = pagination.get('current', 1)
        page_size = pagination.get('pageSize', 20)
    else:
        current_page = 1
        page_size = 20
    
    try:
        # 调用API获取数据
        page_obj = {
            'page': current_page,
            'page_size': page_size
        }
        
        response = get_phone_data_list_api(page_obj, query_params)
        
        if response and response.get('code') == 0:
            data = response.get('data', {})
            rows = data.get('rows', [])
            total_count = data.get('total', 0)
            
            # 处理表格数据
            table_data = []
            for i, row in enumerate(rows):
                table_data.append({
                    'key': str(row.get('id', i)),
                    'id': row.get('id'),
                    'phone_number': row.get('phone_number', ''),
                    'phone_type': row.get('phone_type', ''),
                    'province': row.get('province', ''),
                    'city': row.get('city', ''),
                    'area_code': row.get('area_code', ''),
                    'operator': row.get('operator', ''),
                    'mark_count': row.get('mark_count', 0),
                    'mark_type': row.get('mark_type', ''),
                    'status': row.get('status', ''),
                    'created_at': row.get('created_at', ''),
                    'operation': [
                        {
                            'content': '编辑',
                            'type': 'link',
                            'icon': 'antd-edit',
                            'custom': {
                                'type': 'phone-data-operation-button',
                                'index': 'edit'
                            }
                        },
                        {
                            'content': '删除',
                            'type': 'link',
                            'icon': 'antd-delete',
                            'custom': {
                                'type': 'phone-data-operation-button',
                                'index': 'delete'
                            }
                        }
                    ]
                })
            
            # 更新分页信息
            pagination_config = {
                'current': current_page,
                'pageSize': page_size,
                'total': total_count,
                'showSizeChanger': True,
                'pageSizeOptions': [10, 20, 50, 100],
                'showQuickJumper': True,
                'showLessItems': True
            }
            
            return table_data, pagination_config, False
        else:
            return [], {'current': 1, 'pageSize': 20, 'total': 0}, False
            
    except Exception as e:
        print(f"查询电话数据失败: {str(e)}")
        return [], {'current': 1, 'pageSize': 20, 'total': 0}, False


# 新增/编辑模态框控制回调
@app.callback(
    [
        Output('phone-data-modal', 'visible'),
        Output('phone-data-modal', 'title'),
        Output('phone-data-phone-number-modal-input', 'value'),
        Output('phone-data-phone-type-modal-select', 'value'),
        Output('phone-data-province-modal-select', 'value'),
        Output('phone-data-city-modal-select', 'value'),
        Output('phone-data-operator-modal-select', 'value'),
        Output('phone-data-mark-count-modal-input', 'value'),
        Output('phone-data-mark-type-modal-select', 'value'),
        Output('phone-data-edit-id-store', 'data')
    ],
    [
        Input('phone-data-add-btn', 'n_clicks'),
        Input({'type': 'phone-data-operation-button', 'index': 'edit'}, 'n_clicks'),
        Input('phone-data-table', 'nClicksButton')
    ],
    [
        State('phone-data-table', 'clickedContent'),
        State('phone-data-table', 'recentlyButtonClickedRow')
    ],
    prevent_initial_call=True
)
def control_phone_data_modal(add_clicks, edit_clicks, table_button_clicks, 
                           clicked_content, recently_clicked_row):
    """
    控制新增/编辑模态框的显示和数据填充
    """
    trigger_id = ctx.triggered_id
    
    if trigger_id == 'phone-data-add-btn':
        # 新增模式
        return (
            True,  # 显示模态框
            '新增号码数据',  # 标题
            '',    # 清空电话号码
            '',    # 清空号码类型
            '',    # 清空省份
            '',    # 清空城市
            '',    # 清空运营商
            0,     # 清空标记次数
            '',    # 清空标记类型
            None   # 清空编辑ID
        )
    
    elif trigger_id and trigger_id.get('type') == 'phone-data-operation-button' and trigger_id.get('index') == 'edit':
        # 编辑模式
        if recently_clicked_row:
            row_data = recently_clicked_row
            return (
                True,  # 显示模态框
                '编辑号码数据',  # 标题
                row_data.get('phone_number', ''),
                row_data.get('phone_type', ''),
                row_data.get('province', ''),
                row_data.get('city', ''),
                row_data.get('operator', ''),
                row_data.get('mark_count', 0),
                row_data.get('mark_type', ''),
                row_data.get('id')  # 编辑ID
            )
    
    return dash.no_update


# 保存号码数据回调
@app.callback(
    [
        Output('phone-data-modal', 'visible', allow_duplicate=True),
        Output('phone-data-message-container', 'children', allow_duplicate=True),
        Output('phone-data-search-btn', 'n_clicks', allow_duplicate=True)
    ],
    [
        Input('phone-data-modal', 'okCounts')
    ],
    [
        State('phone-data-phone-number-modal-input', 'value'),
        State('phone-data-phone-type-modal-select', 'value'),
        State('phone-data-province-modal-select', 'value'),
        State('phone-data-city-modal-select', 'value'),
        State('phone-data-operator-modal-select', 'value'),
        State('phone-data-mark-count-modal-input', 'value'),
        State('phone-data-mark-type-modal-select', 'value'),
        State('phone-data-edit-id-store', 'data')
    ],
    prevent_initial_call=True
)
def save_phone_data(confirm_clicks, phone_number, phone_type, province, city, 
                   operator, mark_count, mark_type, edit_id):
    """
    保存号码数据（新增或编辑）
    """
    if confirm_clicks:
        try:
            # 数据验证
            if not phone_number:
                return dash.no_update, fac.AntdMessage(content='请输入电话号码', type='warning'), dash.no_update
            
            if not phone_type:
                return dash.no_update, fac.AntdMessage(content='请选择号码类型', type='warning'), dash.no_update
            
            # 构建保存数据
            save_data = {
                'phone_number': phone_number,
                'phone_type': phone_type,
                'province': province,
                'city': city,
                'operator': operator,
                'mark_count': mark_count or 0,
                'mark_type': mark_type
            }
            
            if edit_id:
                # 编辑模式
                save_data['id'] = edit_id
                response = edit_phone_data_api(save_data)
                if response and response.get('code') == 0:
                    message = '号码数据更新成功'
                else:
                    error_msg = response.get('msg', '号码数据更新失败') if response else '号码数据更新失败'
                    return dash.no_update, fac.AntdMessage(content=error_msg, type='error'), dash.no_update
            else:
                # 新增模式
                response = add_phone_data_api(save_data)
                if response and response.get('code') == 0:
                    message = '号码数据添加成功'
                else:
                    error_msg = response.get('msg', '号码数据添加失败') if response else '号码数据添加失败'
                    return dash.no_update, fac.AntdMessage(content=error_msg, type='error'), dash.no_update
            
            # 成功后关闭模态框，显示成功消息，触发表格刷新
            return (
                False,  # 关闭模态框
                fac.AntdMessage(content=message, type='success'),  # 显示成功消息
                1 if not confirm_clicks else confirm_clicks + 1  # 触发表格刷新
            )
            
        except Exception as e:
            return (
                dash.no_update,
                fac.AntdMessage(content=f'操作失败: {str(e)}', type='error'),
                dash.no_update
            )
    
    return dash.no_update


# 批量删除回调
@app.callback(
    [
        Output('phone-data-message-container', 'children', allow_duplicate=True),
        Output('phone-data-search-btn', 'n_clicks', allow_duplicate=True)
    ],
    [
        Input('phone-data-batch-delete-btn', 'n_clicks')
    ],
    [
        State('phone-data-table', 'selectedRowKeys')
    ],
    prevent_initial_call=True
)
def batch_delete_phone_data(delete_clicks, selected_keys):
    """
    批量删除电话号码数据
    """
    if delete_clicks and selected_keys:
        try:
            # 转换为整数ID列表
            ids = [int(key) for key in selected_keys]
            response = delete_phone_data_api(ids)
            
            if response and response.get('code') == 0:
                return (
                    fac.AntdMessage(content=f'成功删除 {len(ids)} 条记录', type='success'),
                    delete_clicks + 1  # 触发表格刷新
                )
            else:
                error_msg = response.get('msg', '删除失败') if response else '删除失败'
                return fac.AntdMessage(content=error_msg, type='error'), dash.no_update
                
        except Exception as e:
            return (
                fac.AntdMessage(content=f'删除失败: {str(e)}', type='error'),
                dash.no_update
            )
    
    return dash.no_update
