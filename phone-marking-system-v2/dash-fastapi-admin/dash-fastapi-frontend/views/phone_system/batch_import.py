"""
批量导入页面
支持大批量号码导入，进度跟踪，断点续传等功能
"""
import dash
from dash import html, dcc, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime
import json
import base64
import io

from server import app
from api.phone_data import (
    batch_import_phone_data_api,
    get_batch_task_list_api,
    get_batch_task_detail_api,
    cancel_batch_task_api,
    restart_batch_task_api
)
import pandas as pd
import io


def render_batch_import(button_perms=None, role_perms=None):
    """
    渲染批量导入页面
    """
    return html.Div([
        # 页面标题
        fac.AntdPageHeader(
            title='批量导入',
            subTitle='支持Excel、CSV等格式的大批量号码导入',
            ghost=False
        ),
        
        # 导入配置区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdForm([
                        fac.AntdFormItem([
                            fac.AntdSpace([
                                fac.AntdUpload(
                                    id='file-upload',
                                    apiUrl='/api/upload',
                                    fileMaxSize=100,
                                    multiple=False,
                                    fileTypes=['.xlsx', '.xls', '.csv', '.txt'],
                                    buttonContent='点击或拖拽文件到此区域上传',
                                    uploadId='batch-import-upload'
                                ),
                                fac.AntdButton(
                                    '下载模板',
                                    id='download-template-btn',
                                    type='link',
                                    icon=fac.AntdIcon(icon='antd-download'),
                                    style={'marginTop': '8px'}
                                )
                            ], direction='vertical', style={'width': '100%'})
                        ], label='选择文件'),

                        # 文件验证消息
                        html.Div(id='file-validation-message', style={'display': 'none'}),
                        
                        fac.AntdFormItem([
                            fac.AntdInputNumber(
                                id='batch-size-input',
                                value=1000,
                                min=100,
                                max=10000,
                                step=100,
                                style={'width': '100%'}
                            )
                        ], label='批次大小'),
                        
                        fac.AntdFormItem([
                            fac.AntdSwitch(
                                id='auto-process-switch',
                                checked=True
                            )
                        ], label='自动处理'),
                        
                        fac.AntdFormItem([
                            fac.AntdSelect(
                                id='phone-type-select',
                                options=[
                                    {'label': '自动识别', 'value': 'auto'},
                                    {'label': '手机号码', 'value': 'mobile'},
                                    {'label': '固定电话', 'value': 'landline'}
                                ],
                                value='auto',
                                style={'width': '100%'}
                            )
                        ], label='号码类型'),
                        
                        fac.AntdFormItem([
                            fac.AntdInput(
                                id='import-description',
                                placeholder='请输入导入说明（可选）',
                                maxLength=500,
                                mode='text-area',
                                autoSize={'minRows': 3, 'maxRows': 5}
                            )
                        ], label='导入说明')
                    ], layout='vertical')
                ], span=12),
                
                fac.AntdCol([
                    fac.AntdCard([
                        fac.AntdTitle('导入说明', level=4),
                        html.Ul([
                            html.Li('支持Excel (.xlsx, .xls)、CSV (.csv)、文本 (.txt) 格式'),
                            html.Li('Excel文件请将号码放在第一列，可包含标题行'),
                            html.Li('CSV文件请使用逗号分隔，第一列为号码'),
                            html.Li('文本文件每行一个号码'),
                            html.Li('单次最多支持10万个号码'),
                            html.Li('系统会自动去重和格式验证'),
                            html.Li('支持断点续传，意外中断可继续处理')
                        ]),
                        fac.AntdDivider(),
                        fac.AntdTitle('示例格式', level=5),
                        html.Pre(
                            '13800138000\n13900139000\n01012345678\n02087654321',
                            style={
                                'fontSize': '12px',
                                'backgroundColor': '#f5f5f5',
                                'padding': '8px',
                                'borderRadius': '4px',
                                'border': '1px solid #d9d9d9'
                            }
                        )
                    ], size='small')
                ], span=12)
            ], gutter=24),
            
            # 操作按钮
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '开始导入',
                            id='start-import-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-upload'),
                            size='large'
                        ),
                        fac.AntdButton(
                            '清空配置',
                            id='clear-config-btn',
                            icon=fac.AntdIcon(icon='antd-clear'),
                            size='large'
                        ),
                        fac.AntdButton(
                            '下载模板',
                            id='download-template-btn',
                            icon=fac.AntdIcon(icon='antd-download'),
                            size='large'
                        )
                    ])
                ], span=24, style={'textAlign': 'center', 'marginTop': 24})
            ])
        ], title='导入配置', style={'marginBottom': 24}),
        
        # 任务列表
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '刷新',
                            id='refresh-tasks-btn',
                            icon=fac.AntdIcon(icon='antd-reload'),
                            type='default'
                        ),
                        fac.AntdSelect(
                            id='task-status-filter',
                            options=[
                                {'label': '全部状态', 'value': 'all'},
                                {'label': '等待中', 'value': 'pending'},
                                {'label': '运行中', 'value': 'running'},
                                {'label': '已完成', 'value': 'completed'},
                                {'label': '已失败', 'value': 'failed'},
                                {'label': '已取消', 'value': 'cancelled'}
                            ],
                            value='all',
                            style={'width': 120}
                        )
                    ])
                ], span=24, style={'marginBottom': 16})
            ]),
            
            # 高性能分页表格
            fac.AntdSpin([
                fac.AntdTable(
                    id='batch-tasks-table',
                    columns=[
                        {
                            'title': '选择',
                            'dataIndex': 'selection',
                            'width': 50,
                            'renderOptions': {'renderType': 'checkbox'},
                            'fixed': 'left'
                        },
                        {'title': '任务名称', 'dataIndex': 'task_name', 'width': 200, 'fixed': 'left'},
                        {'title': '文件名', 'dataIndex': 'file_name', 'width': 180},
                        {'title': '文件大小', 'dataIndex': 'file_size', 'width': 100, 'sorter': True},
                        {'title': '总数量', 'dataIndex': 'total_count', 'width': 100, 'sorter': True},
                        {'title': '已处理', 'dataIndex': 'processed_count', 'width': 100, 'sorter': True},
                        {'title': '成功', 'dataIndex': 'success_count', 'width': 80, 'sorter': True},
                        {'title': '失败', 'dataIndex': 'failed_count', 'width': 80, 'sorter': True},
                        {'title': '进度', 'dataIndex': 'progress', 'width': 120},
                        {'title': '状态', 'dataIndex': 'status', 'width': 100},
                        {'title': '耗时', 'dataIndex': 'duration', 'width': 100},
                        {'title': '开始时间', 'dataIndex': 'start_time', 'width': 150, 'sorter': True},
                        {'title': '操作', 'dataIndex': 'operation', 'width': 200, 'fixed': 'right'}
                    ],
                    data=[],
                    pagination={
                        'pageSize': 20,
                        'current': 1,
                        'showSizeChanger': True,
                        'showQuickJumper': True,
                        'total': 0,
                        'pageSizeOptions': [10, 20, 50, 100],
                        'showLessItems': True
                    },
                    rowSelectionType='checkbox',
                    selectedRowKeys=[],
                    bordered=True,
                    size='small',
                    maxWidth=1400,
                    maxHeight=400
                )
            ], spinning=False, id='batch-tasks-table-spin')
        ], title='导入任务列表'),
        
        # 进度监控模态框
        fac.AntdModal(
            id='progress-modal',
            title='导入进度监控',
            visible=False,
            width=800,
            children=[
                html.Div(id='progress-content')
            ]
        ),
        
        # 任务详情模态框
        fac.AntdModal(
            id='task-detail-modal',
            title='任务详情',
            visible=False,
            width=1000,
            children=[
                html.Div(id='task-detail-content')
            ]
        ),
        
        # 消息提示容器
        html.Div(id='import-message-container'),

        # 存储组件 - 用于性能优化
        dcc.Store(id='batch-tasks-search-params', data={}),
        dcc.Store(id='batch-tasks-selected-keys', data=[]),
        dcc.Store(id='batch-tasks-current-page', data=1),
        dcc.Store(id='batch-tasks-page-size', data=20),

        # 定时刷新
        dcc.Interval(
            id='import-interval',
            interval=5*1000,  # 5秒刷新一次
            n_intervals=0,
            disabled=True
        )
    ])


# 开始导入回调
@app.callback(
    [Output('import-message-container', 'children'),
     Output('import-interval', 'disabled', allow_duplicate=True),
     Output('progress-modal', 'visible')],
    [Input('start-import-btn', 'n_clicks')],
    [State('file-upload', 'lastUploadTaskRecord'),
     State('batch-size-input', 'value'),
     State('auto-process-switch', 'checked'),
     State('phone-type-select', 'value'),
     State('import-description', 'value')],
    prevent_initial_call=True
)
def start_import(n_clicks, upload_record, batch_size, auto_process, phone_type, description):
    """
    开始导入处理
    """
    if not n_clicks or not upload_record:
        return dash.no_update, dash.no_update, dash.no_update
    
    try:
        # 构建导入配置
        config = {
            'batch_size': batch_size,
            'auto_process': auto_process,
            'phone_type': phone_type,
            'description': description or ''
        }
        
        # 调用导入API
        file_content = upload_record.get('fileContent')
        if file_content:
            # 解码文件内容
            file_data = base64.b64decode(file_content.split(',')[1])
            
            response = batch_import_phone_data_api(file_data, config)
            
            if response and response.get('code') == 200:
                message = fac.AntdMessage(
                    content='导入任务已启动，请查看任务列表监控进度',
                    type='success'
                )
                return message, False, True  # 启用定时刷新，显示进度模态框
            else:
                error_msg = response.get('message', '导入失败') if response else '网络错误'
                message = fac.AntdMessage(
                    content=f'导入失败：{error_msg}',
                    type='error'
                )
                return message, dash.no_update, dash.no_update
        else:
            message = fac.AntdMessage(
                content='请先选择要导入的文件',
                type='warning'
            )
            return message, dash.no_update, dash.no_update
            
    except Exception as e:
        message = fac.AntdMessage(
            content=f'导入异常：{str(e)}',
            type='error'
        )
        return message, dash.no_update, dash.no_update


# 刷新任务列表回调
@app.callback(
    Output('batch-tasks-table', 'data'),
    [Input('refresh-tasks-btn', 'n_clicks'),
     Input('import-interval', 'n_intervals'),
     Input('task-status-filter', 'value')],
    prevent_initial_call=True
)
def refresh_task_list(refresh_clicks, n_intervals, status_filter):
    """
    刷新任务列表
    """
    try:
        # 构建查询参数
        query_params = {}
        if status_filter and status_filter != 'all':
            query_params['status'] = status_filter
        
        response = get_batch_task_list_api({'page': 1, 'page_size': 100}, query_params)
        
        if response and response.get('code') == 200:
            tasks = response.get('data', {}).get('rows', [])
            
            # 格式化任务数据
            formatted_tasks = []
            for task in tasks:
                # 格式化进度
                progress = task.get('progress', 0)
                progress_component = fac.AntdProgress(
                    percent=progress,
                    size='small',
                    status='active' if task.get('status') == 'running' else 'normal'
                )
                
                # 格式化状态
                status = task.get('status', '')
                status_color = {
                    'pending': 'default',
                    'running': 'processing',
                    'completed': 'success',
                    'failed': 'error',
                    'cancelled': 'warning'
                }.get(status, 'default')
                
                status_component = fac.AntdTag(
                    content=status,
                    color=status_color
                )
                
                # 操作按钮
                operation_buttons = fac.AntdSpace([
                    fac.AntdButton(
                        '详情',
                        id={'type': 'task-detail-btn', 'index': task.get('id')},
                        type='link',
                        size='small'
                    ),
                    fac.AntdButton(
                        '取消',
                        id={'type': 'cancel-task-btn', 'index': task.get('id')},
                        type='link',
                        size='small',
                        danger=True,
                        disabled=status not in ['pending', 'running']
                    ),
                    fac.AntdButton(
                        '重启',
                        id={'type': 'restart-task-btn', 'index': task.get('id')},
                        type='link',
                        size='small',
                        disabled=status not in ['failed', 'cancelled']
                    )
                ])
                
                formatted_tasks.append({
                    'key': str(task.get('id')),
                    'task_name': task.get('task_name', ''),
                    'file_name': task.get('file_name', ''),
                    'total_count': task.get('total_count', 0),
                    'processed_count': task.get('processed_count', 0),
                    'success_count': task.get('success_count', 0),
                    'failed_count': task.get('failed_count', 0),
                    'progress': progress_component,
                    'status': status_component,
                    'start_time': task.get('start_time', ''),
                    'operation': operation_buttons
                })
            
            return formatted_tasks
        else:
            return []
            
    except Exception as e:
        return []


# 清空配置回调
@app.callback(
    [Output('file-upload', 'lastUploadTaskRecord'),
     Output('batch-size-input', 'value'),
     Output('auto-process-switch', 'checked'),
     Output('phone-type-select', 'value'),
     Output('import-description', 'value')],
    [Input('clear-config-btn', 'n_clicks')],
    prevent_initial_call=True
)
def clear_config(n_clicks):
    """
    清空配置
    """
    if n_clicks:
        return None, 1000, True, 'auto', ''
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update


# 批量操作按钮状态控制
@app.callback(
    [Output('batch-cancel-btn', 'disabled'),
     Output('batch-retry-btn', 'disabled')],
    [Input('batch-tasks-table', 'selectedRowKeys')],
    prevent_initial_call=True
)
def update_batch_buttons_state(selected_keys):
    """
    根据选择的任务更新批量操作按钮状态
    """
    has_selection = selected_keys and len(selected_keys) > 0
    return not has_selection, not has_selection


# 控制自动刷新定时器
@app.callback(
    Output('import-interval', 'disabled'),
    [Input('auto-refresh-switch', 'checked')],
    prevent_initial_call=True
)
def control_auto_refresh_timer(auto_refresh_enabled):
    """
    控制自动刷新定时器的启用/禁用
    """
    return not auto_refresh_enabled


# 模板下载回调
@app.callback(
    Output('download-template-btn', 'href'),
    Input('download-template-btn', 'n_clicks'),
    prevent_initial_call=True
)
def download_template(n_clicks):
    """
    生成并下载导入模板
    """
    if not n_clicks:
        return dash.no_update

    # 创建模板数据
    template_data = {
        '电话号码': [
            '13800138000',
            '13800138001',
            '13800138002',
            '010-12345678',
            '021-87654321'
        ],
        '号码类型': [
            '手机',
            '手机',
            '手机',
            '固话',
            '固话'
        ],
        '备注': [
            '示例手机号码1',
            '示例手机号码2',
            '示例手机号码3',
            '示例固定电话1',
            '示例固定电话2'
        ]
    }

    # 创建DataFrame
    df = pd.DataFrame(template_data)

    # 生成Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='电话号码导入模板', index=False)

        # 添加说明工作表
        instructions = pd.DataFrame({
            '导入说明': [
                '1. 电话号码列：必填，支持手机号码和固定电话',
                '2. 号码类型列：可选，填写"手机"或"固话"',
                '3. 备注列：可选，用于标记号码用途',
                '4. 手机号码格式：11位数字，如13800138000',
                '5. 固话格式：区号-号码，如010-12345678',
                '6. 每次最多导入10000条记录',
                '7. 支持.xlsx、.xls、.csv格式文件',
                '8. 文件大小不超过100MB'
            ]
        })
        instructions.to_excel(writer, sheet_name='导入说明', index=False)

    output.seek(0)

    # 生成下载链接
    encoded = base64.b64encode(output.read()).decode()
    href = f"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{encoded}"

    return href


# 文件格式验证回调
@app.callback(
    [Output('file-validation-message', 'children'),
     Output('file-validation-message', 'style')],
    Input('file-upload', 'lastUploadTaskRecord'),
    prevent_initial_call=True
)
def validate_upload_file(upload_record):
    """
    验证上传文件格式和内容
    """
    if not upload_record or upload_record.get('taskStatus') != 'success':
        return dash.no_update, dash.no_update

    try:
        # 这里应该调用后端API进行文件验证
        # 暂时返回成功消息
        return fac.AntdAlert(
            message='文件上传成功',
            description='文件格式正确，可以开始导入处理',
            type='success',
            showIcon=True,
            style={'marginTop': '10px'}
        ), {'display': 'block'}

    except Exception as e:
        return fac.AntdAlert(
            message='文件验证失败',
            description=f'文件格式错误或内容不符合要求：{str(e)}',
            type='error',
            showIcon=True,
            style={'marginTop': '10px'}
        ), {'display': 'block'}
