"""
数据统计页面 - 提供多维度数据分析和可视化
"""
import dash
from dash import dcc, html, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_utils_components as fuc
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, date, timedelta
import pandas as pd
import json


def render_data_statistics(button_perms=None, role_perms=None):
    """
    渲染数据统计页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '数据统计'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 统计概览卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总记录数',
                        value=125680,
                        valueStyle={'color': '#3f8600'},
                        prefix=fac.AntdIcon(icon='antd-database')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='已完成',
                        value=98456,
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-check-circle')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='处理中',
                        value=15234,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-sync')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='失败数',
                        value=1990,
                        valueStyle={'color': '#f5222d'},
                        prefix=fac.AntdIcon(icon='antd-close-circle')
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 时间范围选择
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpace([
                        fac.AntdText('统计时间范围：'),
                        fac.AntdDatePicker(
                            id='stats-date-range',
                            value=datetime.now().strftime('%Y-%m-%d'),
                            placeholder='选择统计日期'
                        ),
                        fac.AntdButton(
                            '刷新统计',
                            id='stats-refresh-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-reload')
                        )
                    ])
                ])
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 图表区域
        fac.AntdRow([
            # 处理状态分布饼图
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='status-pie-chart',
                        config={'displayModeBar': False}
                    )
                ], title='处理状态分布')
            ], span=12),
            
            # 标记类型分布柱状图
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='mark-type-bar-chart',
                        config={'displayModeBar': False}
                    )
                ], title='标记类型分布')
            ], span=12)
        ], gutter=16, style={'marginBottom': 16}),
        
        fac.AntdRow([
            # 每日处理量趋势图
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='daily-trend-chart',
                        config={'displayModeBar': False}
                    )
                ], title='每日处理量趋势')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        fac.AntdRow([
            # 省份分布地图
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='province-map-chart',
                        config={'displayModeBar': False}
                    )
                ], title='省份分布')
            ], span=12),
            
            # 运营商分布
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='operator-chart',
                        config={'displayModeBar': False}
                    )
                ], title='运营商分布')
            ], span=12)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 详细统计表格
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTabs([
                        {
                            'label': '按省份统计',
                            'key': 'province',
                            'children': fac.AntdTable(
                                id='province-stats-table',
                                columns=[
                                    {'title': '省份', 'dataIndex': 'province'},
                                    {'title': '总数', 'dataIndex': 'total'},
                                    {'title': '已完成', 'dataIndex': 'completed'},
                                    {'title': '处理中', 'dataIndex': 'processing'},
                                    {'title': '失败', 'dataIndex': 'failed'},
                                    {'title': '完成率', 'dataIndex': 'completion_rate'}
                                ],
                                data=[],
                                pagination={'pageSize': 10},
                                bordered=True,
                                size='small'
                            )
                        },
                        {
                            'label': '按标记类型统计',
                            'key': 'mark_type',
                            'children': fac.AntdTable(
                                id='mark-type-stats-table',
                                columns=[
                                    {'title': '标记类型', 'dataIndex': 'mark_type'},
                                    {'title': '数量', 'dataIndex': 'count'},
                                    {'title': '占比', 'dataIndex': 'percentage'},
                                    {'title': '平均置信度', 'dataIndex': 'avg_confidence'}
                                ],
                                data=[],
                                pagination={'pageSize': 10},
                                bordered=True,
                                size='small'
                            )
                        },
                        {
                            'label': '按时间统计',
                            'key': 'time',
                            'children': fac.AntdTable(
                                id='time-stats-table',
                                columns=[
                                    {'title': '日期', 'dataIndex': 'date'},
                                    {'title': '新增', 'dataIndex': 'new_count'},
                                    {'title': '处理完成', 'dataIndex': 'completed_count'},
                                    {'title': '处理效率', 'dataIndex': 'efficiency'}
                                ],
                                data=[],
                                pagination={'pageSize': 15},
                                bordered=True,
                                size='small'
                            )
                        }
                    ], id='stats-detail-tabs')
                ], title='详细统计')
            ], span=24)
        ]),
        
        # 消息提示容器
        html.Div(id='stats-message-container'),
        
        # 存储组件
        dcc.Store(id='stats-data-store', data={}),
        
        # 自动刷新定时器
        dcc.Interval(
            id='stats-auto-refresh',
            interval=60*1000,  # 60秒刷新一次
            n_intervals=0,
            disabled=False
        )
    ]


# 初始化图表数据
@callback(
    [Output('status-pie-chart', 'figure'),
     Output('mark-type-bar-chart', 'figure'),
     Output('daily-trend-chart', 'figure'),
     Output('province-map-chart', 'figure'),
     Output('operator-chart', 'figure'),
     Output('province-stats-table', 'data'),
     Output('mark-type-stats-table', 'data'),
     Output('time-stats-table', 'data')],
    [Input('stats-refresh-btn', 'n_clicks'),
     Input('stats-auto-refresh', 'n_intervals')],
    [State('stats-date-range', 'value')],
    prevent_initial_call=False
)
def update_statistics_charts(refresh_clicks, auto_refresh, date_range):
    """
    更新统计图表和表格数据
    """
    try:
        # 状态分布饼图
        status_data = {
            'labels': ['已完成', '处理中', '待处理', '失败'],
            'values': [98456, 15234, 10000, 1990],
            'colors': ['#52c41a', '#faad14', '#1890ff', '#f5222d']
        }
        
        status_pie = go.Figure(data=[go.Pie(
            labels=status_data['labels'],
            values=status_data['values'],
            marker_colors=status_data['colors'],
            hole=0.3
        )])
        status_pie.update_layout(
            showlegend=True,
            height=300,
            margin=dict(t=20, b=20, l=20, r=20)
        )
        
        # 标记类型柱状图
        mark_types = ['正常号码', '骚扰电话', '诈骗电话', '推销电话', '企业电话']
        mark_counts = [45000, 28000, 15000, 22000, 18000]
        
        mark_bar = go.Figure(data=[go.Bar(
            x=mark_types,
            y=mark_counts,
            marker_color=['#52c41a', '#faad14', '#f5222d', '#fa8c16', '#1890ff']
        )])
        mark_bar.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            xaxis_title='标记类型',
            yaxis_title='数量'
        )
        
        # 每日趋势图
        dates = pd.date_range(start='2025-06-01', end='2025-07-01', freq='D')
        daily_counts = [8000 + i*50 + (i%7)*200 for i in range(len(dates))]
        
        trend_line = go.Figure(data=[go.Scatter(
            x=dates,
            y=daily_counts,
            mode='lines+markers',
            line=dict(color='#1890ff', width=2),
            marker=dict(size=4)
        )])
        trend_line.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            xaxis_title='日期',
            yaxis_title='处理数量'
        )
        
        # 省份分布（简化版）
        provinces = ['广东', '北京', '上海', '浙江', '江苏', '山东', '四川', '湖北']
        province_counts = [25000, 18000, 15000, 12000, 11000, 9000, 8000, 7000]
        
        province_bar = go.Figure(data=[go.Bar(
            x=provinces,
            y=province_counts,
            marker_color='#1890ff'
        )])
        province_bar.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            xaxis_title='省份',
            yaxis_title='数量'
        )
        
        # 运营商分布
        operators = ['中国移动', '中国联通', '中国电信']
        operator_counts = [52000, 38000, 35000]
        
        operator_pie = go.Figure(data=[go.Pie(
            labels=operators,
            values=operator_counts,
            marker_colors=['#52c41a', '#1890ff', '#faad14']
        )])
        operator_pie.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20)
        )
        
        # 表格数据
        province_table_data = [
            {
                'key': str(i),
                'province': provinces[i],
                'total': province_counts[i],
                'completed': int(province_counts[i] * 0.78),
                'processing': int(province_counts[i] * 0.12),
                'failed': int(province_counts[i] * 0.02),
                'completion_rate': f'{78 + i}%'
            }
            for i in range(len(provinces))
        ]
        
        mark_type_table_data = [
            {
                'key': str(i),
                'mark_type': mark_types[i],
                'count': mark_counts[i],
                'percentage': f'{mark_counts[i]/sum(mark_counts)*100:.1f}%',
                'avg_confidence': f'{85 + i*2}%'
            }
            for i in range(len(mark_types))
        ]
        
        time_table_data = [
            {
                'key': str(i),
                'date': f'2025-07-{i+1:02d}',
                'new_count': 8000 + i*50,
                'completed_count': 7500 + i*45,
                'efficiency': f'{(7500 + i*45)/(8000 + i*50)*100:.1f}%'
            }
            for i in range(15)
        ]
        
        return (
            status_pie, mark_bar, trend_line, province_bar, operator_pie,
            province_table_data, mark_type_table_data, time_table_data
        )
        
    except Exception as e:
        # 返回空图表
        empty_fig = go.Figure()
        empty_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            annotations=[dict(text="数据加载失败", showarrow=False)]
        )
        return (empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, [], [], [])
