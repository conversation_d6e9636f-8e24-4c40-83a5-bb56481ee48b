"""
设备监控页面 - 实时监控设备状态和性能
"""
import dash
from server import appfrom dash import dcc, html, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_utils_components as fuc
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json


def render_device_monitor(button_perms=None, role_perms=None):
    """
    渲染设备监控页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '设备监控'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 设备状态概览
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='在线设备',
                        value=8,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-mobile')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='离线设备',
                        value=2,
                        valueStyle={'color': '#f5222d'},
                        prefix=fac.AntdIcon(icon='antd-disconnect')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='处理中设备',
                        value=5,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-loading')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='设备利用率',
                        value=75.5,
                        precision=1,
                        suffix='%',
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-dashboard')
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 实时监控图表
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='device-cpu-chart',
                        config={'displayModeBar': False}
                    )
                ], title='CPU使用率')
            ], span=12),
            
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='device-memory-chart',
                        config={'displayModeBar': False}
                    )
                ], title='内存使用率')
            ], span=12)
        ], gutter=16, style={'marginBottom': 16}),
        
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='device-network-chart',
                        config={'displayModeBar': False}
                    )
                ], title='网络流量')
            ], span=12),
            
            fac.AntdCol([
                fac.AntdCard([
                    dcc.Graph(
                        id='device-task-chart',
                        config={'displayModeBar': False}
                    )
                ], title='任务处理量')
            ], span=12)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 设备列表
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '刷新状态',
                            id='device-refresh-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-reload')
                        ),
                        fac.AntdButton(
                            '批量重启',
                            id='device-batch-restart-btn',
                            danger=True,
                            icon=fac.AntdIcon(icon='antd-redo')
                        ),
                        fac.AntdSwitch(
                            id='device-auto-refresh-switch',
                            checked=True,
                            checkedChildren='自动刷新',
                            unCheckedChildren='手动刷新'
                        )
                    ], style={'marginBottom': 16}),
                    
                    fac.AntdTable(
                        id='device-monitor-table',
                        columns=[
                            {'title': '设备ID', 'dataIndex': 'device_id', 'width': 120},
                            {'title': '设备名称', 'dataIndex': 'device_name', 'width': 150},
                            {'title': '设备类型', 'dataIndex': 'device_type', 'width': 100},
                            {'title': '连接状态', 'dataIndex': 'status', 'width': 100},
                            {'title': 'CPU使用率', 'dataIndex': 'cpu_usage', 'width': 100},
                            {'title': '内存使用率', 'dataIndex': 'memory_usage', 'width': 100},
                            {'title': '网络延迟', 'dataIndex': 'network_latency', 'width': 100},
                            {'title': '当前任务', 'dataIndex': 'current_task', 'width': 120},
                            {'title': '处理速度', 'dataIndex': 'process_speed', 'key': 'process_speed', 'width': 100},
                            {'title': '最后更新', 'dataIndex': 'last_update', 'key': 'last_update', 'width': 150},
                            {'title': '操作', 'dataIndex': 'operation', 'key': 'operation', 'width': 200, 'fixed': 'right'}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 10,
                            'current': 1,
                            'showSizeChanger': True
                        },
                        rowSelectionType='checkbox',
                        selectedRowKeys=[],
                        bordered=True,
                        size='small',
                        maxWidth=1200
                    )
                ], title='设备状态列表')
            ], span=24)
        ]),
        
        # 设备详情模态框
        fac.AntdModal(
            id='device-detail-modal',
            title='设备详情',
            visible=False,
            width=800,
            children=[
                fac.AntdTabs([
                    {
                        'label': '基本信息',
                        'key': 'basic',
                        'children': fac.AntdDescriptions(
                            id='device-basic-info',
                            items=[],
                            bordered=True,
                            column=2
                        )
                    },
                    {
                        'label': '性能监控',
                        'key': 'performance',
                        'children': html.Div([
                            dcc.Graph(
                                id='device-detail-performance-chart',
                                config={'displayModeBar': False}
                            )
                        ])
                    },
                    {
                        'label': '任务历史',
                        'key': 'tasks',
                        'children': fac.AntdTable(
                            id='device-task-history-table',
                            columns=[
                                {'title': '任务ID', 'dataIndex': 'task_id', 'key': 'task_id'},
                                {'title': '任务类型', 'dataIndex': 'task_type', 'key': 'task_type'},
                                {'title': '开始时间', 'dataIndex': 'start_time', 'key': 'start_time'},
                                {'title': '结束时间', 'dataIndex': 'end_time', 'key': 'end_time'},
                                {'title': '处理数量', 'dataIndex': 'processed', 'key': 'processed'},
                                {'title': '状态', 'dataIndex': 'status', 'key': 'status'}
                            ],
                            data=[],
                            pagination={'pageSize': 5},
                            size='small'
                        )
                    },
                    {
                        'label': '系统日志',
                        'key': 'logs',
                        'children': fac.AntdInput(
                            id='device-logs-textarea',
                            mode='text-area',
                            autoSize={'minRows': 15, 'maxRows': 15},
                            readOnly=True,
                            style={'fontFamily': 'monospace', 'fontSize': '12px'}
                        )
                    }
                ], id='device-detail-tabs')
            ],
            okText='关闭',
            cancelText=None
        ),
        
        # 消息提示容器
        html.Div(id='device-monitor-message-container'),
        
        # 存储组件
        dcc.Store(id='device-monitor-data-store', data={}),
        
        # 自动刷新定时器
        dcc.Interval(
            id='device-monitor-interval',
            interval=5*1000,  # 5秒刷新一次
            n_intervals=0,
            disabled=False
        )
    ]


# 初始化设备监控数据
@app.callback(
    [Output('device-monitor-table', 'data'),
     Output('device-cpu-chart', 'figure'),
     Output('device-memory-chart', 'figure'),
     Output('device-network-chart', 'figure'),
     Output('device-task-chart', 'figure')],
    [Input('device-refresh-btn', 'n_clicks'),
     Input('device-monitor-interval', 'n_intervals')],
    [State('device-auto-refresh-switch', 'checked')],
    prevent_initial_call=False
)
def update_device_monitor(refresh_clicks, auto_refresh, auto_enabled):
    """
    更新设备监控数据
    """
    try:
        # 模拟设备数据
        device_data = []
        device_names = ['Android-001', 'Android-002', 'Android-003', 'iPhone-001', 'iPhone-002', 
                       'Simulator-001', 'Simulator-002', 'Simulator-003', 'Cloud-001', 'Cloud-002']
        
        for i, name in enumerate(device_names):
            status_options = ['online', 'offline', 'busy', 'idle']
            current_status = status_options[i % 4] if i < 8 else 'offline'
            
            status_colors = {
                'online': 'success',
                'offline': 'error', 
                'busy': 'processing',
                'idle': 'warning'
            }
            
            status_texts = {
                'online': '在线',
                'offline': '离线',
                'busy': '忙碌',
                'idle': '空闲'
            }
            
            cpu_usage = 20 + (i * 8) % 60 if current_status != 'offline' else 0
            memory_usage = 30 + (i * 12) % 50 if current_status != 'offline' else 0
            
            device_data.append({
                'key': str(i),
                'device_id': f'DEV_{i+1:03d}',
                'device_name': name,
                'device_type': 'Android' if 'Android' in name else ('iOS' if 'iPhone' in name else 'Simulator'),
                'status': fac.AntdTag(
                    content=status_texts[current_status],
                    color=status_colors[current_status]
                ),
                'cpu_usage': f'{cpu_usage}%' if current_status != 'offline' else '-',
                'memory_usage': f'{memory_usage}%' if current_status != 'offline' else '-',
                'network_latency': f'{50 + i*10}ms' if current_status != 'offline' else '-',
                'current_task': f'处理任务{i+1}' if current_status == 'busy' else '无',
                'process_speed': f'{100 + i*20}/min' if current_status == 'busy' else '-',
                'last_update': datetime.now().strftime('%H:%M:%S'),
                'operation': fac.AntdSpace([
                    fac.AntdButton(
                        '详情',
                        id={'type': 'device-detail-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-eye')
                    ),
                    fac.AntdButton(
                        '重启',
                        id={'type': 'device-restart-btn', 'index': i},
                        type='link',
                        size='small',
                        danger=True,
                        icon=fac.AntdIcon(icon='antd-redo'),
                        disabled=current_status == 'offline'
                    ),
                    fac.AntdButton(
                        '日志',
                        id={'type': 'device-log-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-file-text')
                    )
                ])
            })
        
        # 生成实时图表数据
        import random
        current_time = datetime.now()
        time_points = [current_time - timedelta(minutes=i) for i in range(30, 0, -1)]
        
        # CPU使用率图表
        cpu_fig = go.Figure()
        for i in range(3):  # 显示3个设备的CPU使用率
            cpu_values = [30 + random.randint(-10, 20) for _ in time_points]
            cpu_fig.add_trace(go.Scatter(
                x=time_points,
                y=cpu_values,
                mode='lines+markers',
                name=f'设备{i+1}',
                line=dict(width=2)
            ))
        cpu_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            yaxis_title='CPU使用率 (%)',
            yaxis=dict(range=[0, 100])
        )
        
        # 内存使用率图表
        memory_fig = go.Figure()
        for i in range(3):
            memory_values = [40 + random.randint(-15, 25) for _ in time_points]
            memory_fig.add_trace(go.Scatter(
                x=time_points,
                y=memory_values,
                mode='lines+markers',
                name=f'设备{i+1}',
                line=dict(width=2)
            ))
        memory_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            yaxis_title='内存使用率 (%)',
            yaxis=dict(range=[0, 100])
        )
        
        # 网络流量图表
        network_fig = go.Figure()
        network_values = [random.randint(50, 200) for _ in time_points]
        network_fig.add_trace(go.Scatter(
            x=time_points,
            y=network_values,
            mode='lines+markers',
            name='网络流量',
            line=dict(color='#1890ff', width=2)
        ))
        network_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            yaxis_title='流量 (KB/s)'
        )
        
        # 任务处理量图表
        task_fig = go.Figure()
        task_values = [random.randint(80, 150) for _ in time_points]
        task_fig.add_trace(go.Scatter(
            x=time_points,
            y=task_values,
            mode='lines+markers',
            name='处理量',
            line=dict(color='#52c41a', width=2)
        ))
        task_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            yaxis_title='处理量 (个/分钟)'
        )
        
        return device_data, cpu_fig, memory_fig, network_fig, task_fig
        
    except Exception as e:
        empty_fig = go.Figure()
        empty_fig.update_layout(
            height=300,
            margin=dict(t=20, b=20, l=20, r=20),
            annotations=[dict(text="数据加载失败", showarrow=False)]
        )
        return [], empty_fig, empty_fig, empty_fig, empty_fig


# 控制自动刷新
@app.callback(
    Output('device-monitor-interval', 'disabled'),
    [Input('device-auto-refresh-switch', 'checked')],
    prevent_initial_call=True
)
def control_auto_refresh(auto_enabled):
    """
    控制自动刷新开关
    """
    return not auto_enabled
