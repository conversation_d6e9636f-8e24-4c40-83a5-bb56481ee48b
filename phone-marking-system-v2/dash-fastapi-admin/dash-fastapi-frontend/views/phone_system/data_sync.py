"""
数据同步页面 - 本地与线上数据同步管理
"""
import dash
from dash import dcc, html, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import json


def render_data_sync(button_perms=None, role_perms=None):
    """
    渲染数据同步页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '数据同步'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 同步状态概览
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='本地数据量',
                        value=125680,
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-database')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='线上数据量',
                        value=128456,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-cloud')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='待同步数据',
                        value=2776,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-sync')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='最后同步',
                        value='2小时前',
                        valueStyle={'color': '#722ed1'},
                        prefix=fac.AntdIcon(icon='antd-clock-circle')
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 同步配置
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='sync-direction-select',
                                        options=[
                                            {'label': '本地到线上', 'value': 'local_to_remote'},
                                            {'label': '线上到本地', 'value': 'remote_to_local'},
                                            {'label': '双向同步', 'value': 'bidirectional'}
                                        ],
                                        value='bidirectional',
                                        placeholder='选择同步方向'
                                    )
                                ], label='同步方向')
                            ], span=8),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='sync-mode-select',
                                        options=[
                                            {'label': '增量同步', 'value': 'incremental'},
                                            {'label': '全量同步', 'value': 'full'},
                                            {'label': '差异同步', 'value': 'differential'}
                                        ],
                                        value='incremental',
                                        placeholder='选择同步模式'
                                    )
                                ], label='同步模式')
                            ], span=8),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInputNumber(
                                        id='sync-batch-size-input',
                                        value=1000,
                                        min=100,
                                        max=10000,
                                        placeholder='批次大小'
                                    )
                                ], label='批次大小')
                            ], span=8)
                        ], gutter=16),
                        
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdCheckboxGroup(
                                        id='sync-data-types-checkbox',
                                        options=[
                                            {'label': '电话号码数据', 'value': 'phone_data'},
                                            {'label': '标记信息', 'value': 'mark_info'},
                                            {'label': '用户数据', 'value': 'user_data'},
                                            {'label': '统计数据', 'value': 'statistics'},
                                            {'label': '配置信息', 'value': 'config'}
                                        ],
                                        value=['phone_data', 'mark_info']
                                    )
                                ], label='同步数据类型')
                            ], span=12),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdDatePicker(
                                        id='sync-date-range',
                                        placeholder='选择同步日期'
                                    )
                                ], label='同步时间范围')
                            ], span=12)
                        ], gutter=16)
                    ], layout='vertical')
                ], title='同步配置')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 同步操作
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '开始同步',
                            id='sync-start-btn',
                            type='primary',
                            size='large',
                            icon=fac.AntdIcon(icon='antd-sync')
                        ),
                        fac.AntdButton(
                            '停止同步',
                            id='sync-stop-btn',
                            danger=True,
                            size='large',
                            icon=fac.AntdIcon(icon='antd-stop'),
                            disabled=True
                        ),
                        fac.AntdButton(
                            '检查差异',
                            id='sync-check-diff-btn',
                            size='large',
                            icon=fac.AntdIcon(icon='antd-diff')
                        ),
                        fac.AntdButton(
                            '同步历史',
                            id='sync-history-btn',
                            size='large',
                            icon=fac.AntdIcon(icon='antd-history')
                        )
                    ], size='large'),
                    
                    # 同步进度
                    html.Div([
                        fac.AntdProgress(
                            id='sync-progress',
                            percent=0,
                            status='normal',
                            style={'marginTop': 16, 'display': 'none'}
                        ),
                        fac.AntdText(
                            id='sync-status-text',
                            style={'marginTop': 8, 'display': 'block'}
                        )
                    ])
                ], title='同步操作')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 自动同步设置
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSwitch(
                                        id='auto-sync-enabled-switch',
                                        checked=True,
                                        checkedChildren='启用',
                                        unCheckedChildren='禁用'
                                    )
                                ], label='自动同步')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='auto-sync-interval-select',
                                        options=[
                                            {'label': '每5分钟', 'value': 5},
                                            {'label': '每15分钟', 'value': 15},
                                            {'label': '每30分钟', 'value': 30},
                                            {'label': '每小时', 'value': 60},
                                            {'label': '每2小时', 'value': 120},
                                            {'label': '每6小时', 'value': 360},
                                            {'label': '每天', 'value': 1440}
                                        ],
                                        value=30,
                                        placeholder='选择同步间隔'
                                    )
                                ], label='同步间隔')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdTimePicker(
                                        id='auto-sync-time-picker',
                                        value='02:00:00',
                                        placeholder='选择同步时间'
                                    )
                                ], label='定时同步时间')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdButton(
                                        '保存设置',
                                        id='auto-sync-save-btn',
                                        type='primary',
                                        icon=fac.AntdIcon(icon='antd-save')
                                    )
                                ])
                            ], span=6)
                        ], gutter=16)
                    ], layout='inline')
                ], title='自动同步设置')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 同步状态监控
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='sync-status-table',
                        columns=[
                            {'title': '数据类型', 'dataIndex': 'data_type', 'width': 120},
                            {'title': '本地数量', 'dataIndex': 'local_count', 'width': 100},
                            {'title': '线上数量', 'dataIndex': 'remote_count', 'width': 100},
                            {'title': '差异数量', 'dataIndex': 'diff_count', 'width': 100},
                            {'title': '同步状态', 'dataIndex': 'sync_status', 'width': 100},
                            {'title': '最后同步时间', 'dataIndex': 'last_sync_time', 'width': 150},
                            {'title': '操作', 'dataIndex': 'operation', 'width': 150}
                        ],
                        data=[],
                        pagination=False,
                        bordered=True,
                        size='small'
                    )
                ], title='同步状态监控')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 同步历史记录
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='sync-history-table',
                        columns=[
                            {'title': '同步时间', 'dataIndex': 'sync_time', 'width': 150},
                            {'title': '同步方向', 'dataIndex': 'direction', 'width': 100},
                            {'title': '同步模式', 'dataIndex': 'mode', 'width': 100},
                            {'title': '数据类型', 'dataIndex': 'data_types', 'width': 150},
                            {'title': '处理数量', 'dataIndex': 'processed_count', 'width': 100},
                            {'title': '成功数量', 'dataIndex': 'success_count', 'width': 100},
                            {'title': '失败数量', 'dataIndex': 'failed_count', 'width': 100},
                            {'title': '耗时', 'dataIndex': 'duration', 'width': 80},
                            {'title': '状态', 'dataIndex': 'status', 'key': 'status', 'width': 80},
                            {'title': '操作', 'dataIndex': 'operation', 'key': 'operation', 'width': 120}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 10,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True
                        },
                        bordered=True,
                        size='small'
                    )
                ], title='同步历史记录')
            ], span=24)
        ]),
        
        # 差异详情模态框
        fac.AntdModal(
            id='sync-diff-modal',
            title='数据差异详情',
            visible=False,
            width=1000,
            children=[
                fac.AntdTable(
                    id='sync-diff-table',
                    columns=[
                        {'title': '记录ID', 'dataIndex': 'record_id', 'key': 'record_id'},
                        {'title': '数据类型', 'dataIndex': 'data_type', 'key': 'data_type'},
                        {'title': '差异类型', 'dataIndex': 'diff_type', 'key': 'diff_type'},
                        {'title': '本地值', 'dataIndex': 'local_value', 'key': 'local_value'},
                        {'title': '线上值', 'dataIndex': 'remote_value', 'key': 'remote_value'},
                        {'title': '建议操作', 'dataIndex': 'suggested_action', 'key': 'suggested_action'}
                    ],
                    data=[],
                    pagination={'pageSize': 10},
                    bordered=True,
                    size='small'
                )
            ],
            okText='关闭',
            cancelText=None
        ),
        
        # 消息提示容器
        html.Div(id='sync-message-container'),
        
        # 存储组件
        dcc.Store(id='sync-config-store', data={}),
        dcc.Store(id='sync-progress-store', data={'running': False, 'percent': 0}),
        
        # 自动刷新定时器
        dcc.Interval(
            id='sync-auto-refresh',
            interval=10*1000,  # 10秒刷新一次
            n_intervals=0,
            disabled=False
        )
    ]


# 初始化同步状态数据
@callback(
    [Output('sync-status-table', 'data'),
     Output('sync-history-table', 'data')],
    [Input('sync-auto-refresh', 'n_intervals')],
    prevent_initial_call=False
)
def update_sync_status(auto_refresh):
    """
    更新同步状态数据
    """
    try:
        # 模拟同步状态数据
        status_data = [
            {
                'key': '1',
                'data_type': '电话号码数据',
                'local_count': 125680,
                'remote_count': 128456,
                'diff_count': 2776,
                'sync_status': fac.AntdTag(content='需要同步', color='warning'),
                'last_sync_time': '2025-07-01 10:30:00',
                'operation': fac.AntdButton('立即同步', type='link', size='small')
            },
            {
                'key': '2',
                'data_type': '标记信息',
                'local_count': 89456,
                'remote_count': 89456,
                'diff_count': 0,
                'sync_status': fac.AntdTag(content='已同步', color='success'),
                'last_sync_time': '2025-07-01 12:00:00',
                'operation': fac.AntdButton('查看详情', type='link', size='small')
            },
            {
                'key': '3',
                'data_type': '用户数据',
                'local_count': 1256,
                'remote_count': 1280,
                'diff_count': 24,
                'sync_status': fac.AntdTag(content='需要同步', color='warning'),
                'last_sync_time': '2025-07-01 09:15:00',
                'operation': fac.AntdButton('立即同步', type='link', size='small')
            }
        ]
        
        # 模拟同步历史数据
        history_data = []
        for i in range(15):
            status_options = ['success', 'failed', 'partial']
            current_status = status_options[i % 3]
            
            status_colors = {
                'success': 'success',
                'failed': 'error',
                'partial': 'warning'
            }
            
            status_texts = {
                'success': '成功',
                'failed': '失败', 
                'partial': '部分成功'
            }
            
            processed = 1000 + i * 100
            success = int(processed * (0.95 if current_status == 'success' else 0.7))
            failed = processed - success
            
            history_data.append({
                'key': str(i),
                'sync_time': f'2025-07-{(i % 30) + 1:02d} {10 + (i % 12):02d}:00:00',
                'direction': '双向同步',
                'mode': '增量同步',
                'data_types': '电话号码数据, 标记信息',
                'processed_count': processed,
                'success_count': success,
                'failed_count': failed,
                'duration': f'{5 + (i % 10)}分钟',
                'status': fac.AntdTag(
                    content=status_texts[current_status],
                    color=status_colors[current_status]
                ),
                'operation': fac.AntdSpace([
                    fac.AntdButton('详情', type='link', size='small'),
                    fac.AntdButton('重试', type='link', size='small', disabled=current_status == 'success')
                ])
            })
        
        return status_data, history_data
        
    except Exception as e:
        return [], []


# 开始同步回调
@callback(
    [Output('sync-start-btn', 'disabled'),
     Output('sync-stop-btn', 'disabled'),
     Output('sync-progress', 'style'),
     Output('sync-progress', 'percent'),
     Output('sync-status-text', 'children'),
     Output('sync-message-container', 'children')],
    [Input('sync-start-btn', 'n_clicks'),
     Input('sync-stop-btn', 'n_clicks')],
    [State('sync-direction-select', 'value'),
     State('sync-mode-select', 'value'),
     State('sync-data-types-checkbox', 'value')],
    prevent_initial_call=True
)
def control_sync_process(start_clicks, stop_clicks, direction, mode, data_types):
    """
    控制同步进程
    """
    trigger_id = ctx.triggered_id
    
    if trigger_id == 'sync-start-btn' and start_clicks:
        try:
            # 模拟开始同步
            return (
                True,  # 禁用开始按钮
                False,  # 启用停止按钮
                {'marginTop': 16, 'display': 'block'},  # 显示进度条
                10,  # 初始进度
                '正在初始化同步任务...',
                fac.AntdMessage(content='同步任务已开始', type='success')
            )
            
        except Exception as e:
            return (
                False, True, {'marginTop': 16, 'display': 'none'}, 0, '',
                fac.AntdMessage(content=f'启动同步失败: {str(e)}', type='error')
            )
    
    elif trigger_id == 'sync-stop-btn' and stop_clicks:
        try:
            # 模拟停止同步
            return (
                False,  # 启用开始按钮
                True,  # 禁用停止按钮
                {'marginTop': 16, 'display': 'none'},  # 隐藏进度条
                0,  # 重置进度
                '同步已停止',
                fac.AntdMessage(content='同步任务已停止', type='warning')
            )
            
        except Exception as e:
            return (
                False, True, {'marginTop': 16, 'display': 'none'}, 0, '',
                fac.AntdMessage(content=f'停止同步失败: {str(e)}', type='error')
            )
    
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
