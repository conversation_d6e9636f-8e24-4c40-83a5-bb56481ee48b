"""
系统配置页面 - 管理系统参数和配置
"""
import dash
from dash import dcc, html, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
import json


def render_system_config(button_perms=None, role_perms=None):
    """
    渲染系统配置页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '系统配置'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 配置选项卡
        fac.AntdTabs([
            {
                'label': '基础配置',
                'key': 'basic',
                'children': [
                    fac.AntdCard([
                        fac.AntdForm([
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-system-name',
                                            value='电话标记识别系统',
                                            placeholder='请输入系统名称'
                                        )
                                    ], label='系统名称', required=True)
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-system-version',
                                            value='v2.0.0',
                                            placeholder='请输入系统版本'
                                        )
                                    ], label='系统版本')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-max-concurrent-tasks',
                                            value=10,
                                            min=1,
                                            max=100,
                                            placeholder='最大并发任务数'
                                        )
                                    ], label='最大并发任务数')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-batch-size',
                                            value=1000,
                                            min=100,
                                            max=10000,
                                            placeholder='批处理大小'
                                        )
                                    ], label='批处理大小')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-timeout-seconds',
                                            value=300,
                                            min=30,
                                            max=3600,
                                            placeholder='任务超时时间(秒)'
                                        )
                                    ], label='任务超时时间(秒)')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-retry-times',
                                            value=3,
                                            min=0,
                                            max=10,
                                            placeholder='失败重试次数'
                                        )
                                    ], label='失败重试次数')
                                ], span=12)
                            ], gutter=16)
                        ], layout='vertical')
                    ], title='基础参数配置')
                ]
            },
            {
                'label': '数据库配置',
                'key': 'database',
                'children': [
                    fac.AntdCard([
                        fac.AntdForm([
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-db-host',
                                            value='localhost',
                                            placeholder='数据库主机地址'
                                        )
                                    ], label='数据库主机', required=True)
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-db-port',
                                            value=3306,
                                            min=1,
                                            max=65535,
                                            placeholder='数据库端口'
                                        )
                                    ], label='数据库端口', required=True)
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-db-name',
                                            value='phone_marking_db',
                                            placeholder='数据库名称'
                                        )
                                    ], label='数据库名称', required=True)
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-db-username',
                                            value='root',
                                            placeholder='数据库用户名'
                                        )
                                    ], label='数据库用户名', required=True)
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-db-password',
                                            mode='password',
                                            placeholder='数据库密码'
                                        )
                                    ], label='数据库密码')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-db-pool-size',
                                            value=20,
                                            min=5,
                                            max=100,
                                            placeholder='连接池大小'
                                        )
                                    ], label='连接池大小')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdSpace([
                                        fac.AntdButton(
                                            '测试连接',
                                            id='config-test-db-btn',
                                            type='default',
                                            icon=fac.AntdIcon(icon='antd-api')
                                        )
                                    ])
                                ], span=24)
                            ])
                        ], layout='vertical')
                    ], title='数据库连接配置')
                ]
            },
            {
                'label': '识别配置',
                'key': 'recognition',
                'children': [
                    fac.AntdCard([
                        fac.AntdForm([
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSlider(
                                            id='config-confidence-threshold',
                                            min=0,
                                            max=100,
                                            value=75,
                                            marks={
                                                0: '0%',
                                                25: '25%',
                                                50: '50%',
                                                75: '75%',
                                                100: '100%'
                                            }
                                        )
                                    ], label='置信度阈值')
                                ], span=24)
                            ]),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdCheckboxGroup(
                                            id='config-mark-types',
                                            options=[
                                                {'label': '骚扰电话', 'value': 'harassment'},
                                                {'label': '诈骗电话', 'value': 'fraud'},
                                                {'label': '推销电话', 'value': 'marketing'},
                                                {'label': '企业电话', 'value': 'business'},
                                                {'label': '正常号码', 'value': 'normal'}
                                            ],
                                            value=['harassment', 'fraud', 'marketing', 'business']
                                        )
                                    ], label='启用标记类型')
                                ], span=24)
                            ]),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSwitch(
                                            id='config-auto-learning',
                                            checked=True,
                                            checkedChildren='开启',
                                            unCheckedChildren='关闭'
                                        )
                                    ], label='自动学习')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSwitch(
                                            id='config-real-time-update',
                                            checked=False,
                                            checkedChildren='开启',
                                            unCheckedChildren='关闭'
                                        )
                                    ], label='实时更新')
                                ], span=12)
                            ], gutter=16)
                        ], layout='vertical')
                    ], title='识别算法配置')
                ]
            },
            {
                'label': '通知配置',
                'key': 'notification',
                'children': [
                    fac.AntdCard([
                        fac.AntdForm([
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSwitch(
                                            id='config-email-enabled',
                                            checked=True,
                                            checkedChildren='启用',
                                            unCheckedChildren='禁用'
                                        )
                                    ], label='邮件通知')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSwitch(
                                            id='config-sms-enabled',
                                            checked=False,
                                            checkedChildren='启用',
                                            unCheckedChildren='禁用'
                                        )
                                    ], label='短信通知')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-smtp-server',
                                            value='smtp.qq.com',
                                            placeholder='SMTP服务器地址'
                                        )
                                    ], label='SMTP服务器')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-smtp-port',
                                            value=587,
                                            min=1,
                                            max=65535,
                                            placeholder='SMTP端口'
                                        )
                                    ], label='SMTP端口')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-email-username',
                                            placeholder='发送邮箱用户名'
                                        )
                                    ], label='邮箱用户名')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-email-password',
                                            mode='password',
                                            placeholder='邮箱密码或授权码'
                                        )
                                    ], label='邮箱密码')
                                ], span=12)
                            ], gutter=16)
                        ], layout='vertical')
                    ], title='通知服务配置')
                ]
            },
            {
                'label': '日志配置',
                'key': 'logging',
                'children': [
                    fac.AntdCard([
                        fac.AntdForm([
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdSelect(
                                            id='config-log-level',
                                            options=[
                                                {'label': 'DEBUG', 'value': 'DEBUG'},
                                                {'label': 'INFO', 'value': 'INFO'},
                                                {'label': 'WARNING', 'value': 'WARNING'},
                                                {'label': 'ERROR', 'value': 'ERROR'},
                                                {'label': 'CRITICAL', 'value': 'CRITICAL'}
                                            ],
                                            value='INFO',
                                            placeholder='选择日志级别'
                                        )
                                    ], label='日志级别')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-log-retention-days',
                                            value=30,
                                            min=1,
                                            max=365,
                                            placeholder='日志保留天数'
                                        )
                                    ], label='日志保留天数')
                                ], span=12)
                            ], gutter=16),
                            
                            fac.AntdRow([
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInput(
                                            id='config-log-path',
                                            value='/var/log/phone-marking/',
                                            placeholder='日志文件路径'
                                        )
                                    ], label='日志文件路径')
                                ], span=12),
                                
                                fac.AntdCol([
                                    fac.AntdFormItem([
                                        fac.AntdInputNumber(
                                            id='config-log-max-size',
                                            value=100,
                                            min=1,
                                            max=1000,
                                            placeholder='单个日志文件最大大小(MB)'
                                        )
                                    ], label='最大文件大小(MB)')
                                ], span=12)
                            ], gutter=16)
                        ], layout='vertical')
                    ], title='日志系统配置')
                ]
            }
        ], id='config-tabs'),
        
        # 操作按钮
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '保存配置',
                            id='config-save-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-save')
                        ),
                        fac.AntdButton(
                            '重置配置',
                            id='config-reset-btn',
                            icon=fac.AntdIcon(icon='antd-undo')
                        ),
                        fac.AntdButton(
                            '导出配置',
                            id='config-export-btn',
                            icon=fac.AntdIcon(icon='antd-download')
                        ),
                        fac.AntdButton(
                            '导入配置',
                            id='config-import-btn',
                            icon=fac.AntdIcon(icon='antd-upload')
                        )
                    ], size='large')
                ])
            ], span=24)
        ], style={'marginTop': 16}),
        
        # 配置导入模态框
        fac.AntdModal(
            id='config-import-modal',
            title='导入配置',
            visible=False,
            width=600,
            children=[
                fac.AntdUpload(
                    id='config-upload',
                    apiUrl='/api/config/upload',
                    fileMaxSize=1,
                    fileTypes=['json'],
                    multiple=False,
                    buttonContent='点击或拖拽配置文件到此区域'
                )
            ],
            okText='确定',
            cancelText='取消'
        ),
        
        # 消息提示容器
        html.Div(id='config-message-container'),
        
        # 存储组件
        dcc.Store(id='config-data-store', data={})
    ]


# 保存配置回调
@callback(
    Output('config-message-container', 'children'),
    [Input('config-save-btn', 'n_clicks')],
    [State('config-system-name', 'value'),
     State('config-system-version', 'value'),
     State('config-max-concurrent-tasks', 'value'),
     State('config-batch-size', 'value'),
     State('config-timeout-seconds', 'value'),
     State('config-retry-times', 'value'),
     State('config-db-host', 'value'),
     State('config-db-port', 'value'),
     State('config-db-name', 'value'),
     State('config-db-username', 'value'),
     State('config-confidence-threshold', 'value'),
     State('config-mark-types', 'value'),
     State('config-auto-learning', 'checked'),
     State('config-real-time-update', 'checked'),
     State('config-email-enabled', 'checked'),
     State('config-log-level', 'value'),
     State('config-log-retention-days', 'value')],
    prevent_initial_call=True
)
def save_config(save_clicks, system_name, system_version, max_concurrent, batch_size,
                timeout_seconds, retry_times, db_host, db_port, db_name, db_username,
                confidence_threshold, mark_types, auto_learning, real_time_update,
                email_enabled, log_level, log_retention_days):
    """
    保存系统配置
    """
    if save_clicks:
        try:
            # 构建配置数据
            config_data = {
                'basic': {
                    'system_name': system_name,
                    'system_version': system_version,
                    'max_concurrent_tasks': max_concurrent,
                    'batch_size': batch_size,
                    'timeout_seconds': timeout_seconds,
                    'retry_times': retry_times
                },
                'database': {
                    'host': db_host,
                    'port': db_port,
                    'database': db_name,
                    'username': db_username
                },
                'recognition': {
                    'confidence_threshold': confidence_threshold,
                    'enabled_mark_types': mark_types,
                    'auto_learning': auto_learning,
                    'real_time_update': real_time_update
                },
                'notification': {
                    'email_enabled': email_enabled
                },
                'logging': {
                    'log_level': log_level,
                    'retention_days': log_retention_days
                }
            }
            
            # 模拟保存配置API调用
            # response = save_system_config_api(config_data)
            
            return fac.AntdMessage(
                content='配置保存成功',
                type='success'
            )
            
        except Exception as e:
            return fac.AntdMessage(
                content=f'配置保存失败: {str(e)}',
                type='error'
            )
    
    return dash.no_update


# 测试数据库连接回调
@callback(
    Output('config-message-container', 'children', allow_duplicate=True),
    [Input('config-test-db-btn', 'n_clicks')],
    [State('config-db-host', 'value'),
     State('config-db-port', 'value'),
     State('config-db-name', 'value'),
     State('config-db-username', 'value'),
     State('config-db-password', 'value')],
    prevent_initial_call=True
)
def test_database_connection(test_clicks, host, port, database, username, password):
    """
    测试数据库连接
    """
    if test_clicks:
        try:
            # 模拟数据库连接测试
            # connection_result = test_db_connection(host, port, database, username, password)
            
            # 模拟连接成功
            return fac.AntdMessage(
                content='数据库连接测试成功',
                type='success'
            )
            
        except Exception as e:
            return fac.AntdMessage(
                content=f'数据库连接测试失败: {str(e)}',
                type='error'
            )
    
    return dash.no_update
