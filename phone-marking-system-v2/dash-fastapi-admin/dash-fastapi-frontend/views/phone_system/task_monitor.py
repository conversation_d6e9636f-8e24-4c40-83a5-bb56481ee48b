"""
批量任务监控页面 - 支持高性能分页查询和实时监控
"""
import dash
from server import appfrom dash import dcc, html, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import json

from api.phone_data import *


def render_task_monitor(button_perms=None, role_perms=None):
    """
    渲染批量任务监控页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '任务监控'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 任务统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总任务数',
                        value=0,
                        id='task-total-count',
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-schedule')
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='运行中',
                        value=0,
                        id='task-running-count',
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-play-circle')
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='等待中',
                        value=0,
                        id='task-pending-count',
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-clock-circle')
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='已完成',
                        value=0,
                        id='task-completed-count',
                        valueStyle={'color': '#722ed1'},
                        prefix=fac.AntdIcon(icon='antd-check-circle')
                    )
                ], hoverable=True)
            ], span=6)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 查询表单
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInput(
                                        id='task-name-input',
                                        placeholder='请输入任务名称',
                                        allowClear=True,
                                        prefix=fac.AntdIcon(icon='antd-search')
                                    )
                                ], label='任务名称')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='task-type-select',
                                        placeholder='请选择任务类型',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '导入任务', 'value': 'import'},
                                            {'label': '处理任务', 'value': 'process'},
                                            {'label': '导出任务', 'value': 'export'},
                                            {'label': '同步任务', 'value': 'sync'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='任务类型')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='task-status-select',
                                        placeholder='请选择任务状态',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '等待中', 'value': 'pending'},
                                            {'label': '运行中', 'value': 'running'},
                                            {'label': '已完成', 'value': 'completed'},
                                            {'label': '失败', 'value': 'failed'},
                                            {'label': '已取消', 'value': 'cancelled'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='任务状态')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdDatePicker(
                                        id='task-date-range',
                                        picker='range',
                                        placeholder=['开始时间', '结束时间'],
                                        style={'width': '100%'}
                                    )
                                ], label='创建时间')
                            ], span=6)
                        ], gutter=16),
                        
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdSpace([
                                    fac.AntdButton(
                                        '查询',
                                        id='task-search-btn',
                                        type='primary',
                                        icon=fac.AntdIcon(icon='antd-search')
                                    ),
                                    fac.AntdButton(
                                        '重置',
                                        id='task-reset-btn',
                                        icon=fac.AntdIcon(icon='antd-sync')
                                    ),
                                    fac.AntdButton(
                                        '刷新',
                                        id='task-refresh-btn',
                                        icon=fac.AntdIcon(icon='antd-reload')
                                    )
                                ])
                            ], span=16),
                            
                            fac.AntdCol([
                                fac.AntdSwitch(
                                    id='task-auto-refresh-switch',
                                    checked=True,
                                    checkedChildren='自动刷新',
                                    unCheckedChildren='手动刷新'
                                )
                            ], span=8, style={'textAlign': 'right'})
                        ], style={'marginTop': 16})
                    ], layout='vertical')
                ], size='small')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 操作按钮区域
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '批量取消',
                        id='task-batch-cancel-btn',
                        danger=True,
                        icon=fac.AntdIcon(icon='antd-stop'),
                        disabled=True
                    ),
                    fac.AntdButton(
                        '批量重启',
                        id='task-batch-restart-btn',
                        icon=fac.AntdIcon(icon='antd-redo'),
                        disabled=True
                    ),
                    fac.AntdButton(
                        '清理完成任务',
                        id='task-clean-completed-btn',
                        icon=fac.AntdIcon(icon='antd-clear')
                    )
                ])
            ], span=16),
            
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText('每页显示：'),
                    fac.AntdSelect(
                        id='task-page-size-select',
                        options=[
                            {'label': '10条', 'value': 10},
                            {'label': '20条', 'value': 20},
                            {'label': '50条', 'value': 50}
                        ],
                        value=20,
                        style={'width': 80}
                    )
                ], style={'float': 'right'})
            ], span=8)
        ], style={'marginBottom': 16}),
        
        # 任务列表表格
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdSpin([
                        fac.AntdTable(
                            id='task-monitor-table',
                            columns=[
                                {
                                    'title': '选择',
                                    'dataIndex': 'selection',
                                    'width': 50,
                                    'renderOptions': {'renderType': 'checkbox'},
                                    'fixed': 'left'
                                },
                                {'title': '任务名称', 'dataIndex': 'task_name', 'width': 200, 'fixed': 'left'},
                                {'title': '任务类型', 'dataIndex': 'task_type', 'width': 100},
                                {'title': '文件名', 'dataIndex': 'file_name', 'width': 150},
                                {'title': '总数量', 'dataIndex': 'total_count', 'width': 80, 'sorter': True},
                                {'title': '已处理', 'dataIndex': 'processed_count', 'width': 80, 'sorter': True},
                                {'title': '成功', 'dataIndex': 'success_count', 'width': 80, 'sorter': True},
                                {'title': '失败', 'dataIndex': 'failed_count', 'width': 80, 'sorter': True},
                                {'title': '进度', 'dataIndex': 'progress', 'width': 120},
                                {'title': '状态', 'dataIndex': 'status', 'width': 100},
                                {'title': '开始时间', 'dataIndex': 'start_time', 'width': 150, 'sorter': True},
                                {'title': '耗时', 'dataIndex': 'duration', 'width': 100},
                                {'title': '操作', 'dataIndex': 'operation', 'width': 200, 'fixed': 'right'}
                            ],
                            data=[],
                            pagination={
                                'pageSize': 20,
                                'current': 1,
                                'showSizeChanger': True,
                                'showQuickJumper': True,
                                'total': 0,
                                'pageSizeOptions': ['10', '20', '50'],
                                'showLessItems': True
                            },
                            rowSelectionType='checkbox',
                            selectedRowKeys=[],
                            bordered=True,
                            size='small',
                            maxWidth=1400,
                            maxHeight=500
                        )
                    ], spinning=False, id='task-monitor-table-spin')
                ])
            ], span=24)
        ]),
        
        # 任务详情模态框
        fac.AntdModal(
            id='task-detail-modal',
            title='任务详情',
            visible=False,
            width=800,
            children=[
                html.Div(id='task-detail-content')
            ]
        ),
        
        # 消息提示容器
        html.Div(id='task-monitor-message-container'),
        
        # 存储组件
        dcc.Store(id='task-monitor-search-params', data={}),
        dcc.Store(id='task-monitor-selected-keys', data=[]),
        
        # 实时刷新定时器
        dcc.Interval(
            id='task-monitor-interval',
            interval=5*1000,  # 5秒刷新一次
            n_intervals=0,
            disabled=False
        )
    ]


# 高性能任务监控查询回调
@app.callback(
    [Output('task-monitor-table', 'data'),
     Output('task-monitor-table', 'pagination'),
     Output('task-monitor-table-spin', 'spinning'),
     Output('task-total-count', 'value'),
     Output('task-running-count', 'value'),
     Output('task-pending-count', 'value'),
     Output('task-completed-count', 'value'),
     Output('task-monitor-search-params', 'data'),
     Output('task-monitor-message-container', 'children')],
    [Input('task-search-btn', 'n_clicks'),
     Input('task-refresh-btn', 'n_clicks'),
     Input('task-monitor-table', 'pagination'),
     Input('task-monitor-table', 'sorter'),
     Input('task-page-size-select', 'value'),
     Input('task-monitor-interval', 'n_intervals')],
    [State('task-name-input', 'value'),
     State('task-type-select', 'value'),
     State('task-status-select', 'value'),
     State('task-date-range', 'value'),
     State('task-auto-refresh-switch', 'checked'),
     State('task-monitor-search-params', 'data')],
    prevent_initial_call=False
)
def search_task_monitor_with_performance(
    search_clicks, refresh_clicks, pagination, sorter, page_size_select, auto_refresh_intervals,
    task_name, task_type, task_status, date_range, auto_refresh_enabled, stored_params
):
    """
    高性能任务监控查询
    支持实时刷新、排序、分页等功能
    """
    try:
        # 开始加载
        spinning = True

        # 构建查询参数
        query_params = {}
        if task_name:
            query_params['task_name'] = task_name
        if task_type:
            query_params['task_type'] = task_type
        if task_status:
            query_params['task_status'] = task_status
        if date_range and len(date_range) == 2:
            query_params['start_date'] = date_range[0]
            query_params['end_date'] = date_range[1]

        # 分页参数
        page = pagination.get('current', 1) if pagination else 1
        page_size = page_size_select or pagination.get('pageSize', 20) if pagination else 20

        # 排序参数
        if sorter:
            if sorter.get('order') == 'ascend':
                query_params['order_by'] = sorter.get('field')
                query_params['order_direction'] = 'asc'
            elif sorter.get('order') == 'descend':
                query_params['order_by'] = sorter.get('field')
                query_params['order_direction'] = 'desc'

        # 性能优化：检查是否需要重新查询
        current_params = {
            'query': query_params,
            'page': page,
            'page_size': page_size
        }

        # 如果是自动刷新但开关关闭，跳过更新
        if (ctx.triggered_id == 'task-monitor-interval' and not auto_refresh_enabled):
            return dash.no_update, dash.no_update, False, dash.no_update, dash.no_update, dash.no_update, dash.no_update, current_params, None

        # 模拟API调用
        import time
        import random
        time.sleep(0.1)  # 模拟网络延迟

        # 生成模拟任务数据
        mock_data = []
        start_index = (page - 1) * page_size + 1

        task_types = ['import', 'process', 'export', 'sync']
        task_type_names = {'import': '导入任务', 'process': '处理任务', 'export': '导出任务', 'sync': '同步任务'}
        task_statuses = ['pending', 'running', 'completed', 'failed', 'cancelled']
        status_colors = {
            'pending': 'warning',
            'running': 'processing',
            'completed': 'success',
            'failed': 'error',
            'cancelled': 'default'
        }
        status_names = {
            'pending': '等待中',
            'running': '运行中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        }

        for i in range(start_index, start_index + page_size):
            current_type = task_types[i % len(task_types)]
            current_status = task_statuses[i % len(task_statuses)]

            # 模拟任务进度
            if current_status == 'running':
                progress_value = random.randint(10, 90)
            elif current_status == 'completed':
                progress_value = 100
            else:
                progress_value = 0

            total_count = random.randint(100, 10000)
            processed_count = int(total_count * progress_value / 100)
            success_count = int(processed_count * 0.9) if current_status != 'failed' else 0
            failed_count = processed_count - success_count

            # 计算耗时
            if current_status in ['completed', 'failed', 'cancelled']:
                duration = f"{random.randint(1, 120)}分钟"
            elif current_status == 'running':
                duration = f"{random.randint(1, 60)}分钟"
            else:
                duration = "-"

            mock_data.append({
                'key': str(i),
                'task_name': f'任务_{i:04d}',
                'task_type': fac.AntdTag(
                    content=task_type_names[current_type],
                    color='blue'
                ),
                'file_name': f'phone_data_{i}.xlsx',
                'total_count': f'{total_count:,}',
                'processed_count': f'{processed_count:,}',
                'success_count': f'{success_count:,}',
                'failed_count': f'{failed_count:,}',
                'progress': fac.AntdProgress(
                    percent=progress_value,
                    size='small',
                    status='active' if current_status == 'running' else 'normal'
                ),
                'status': fac.AntdTag(
                    content=status_names[current_status],
                    color=status_colors[current_status]
                ),
                'start_time': f'2025-07-{(i % 30) + 1:02d} {random.randint(8, 18):02d}:{random.randint(0, 59):02d}:00',
                'duration': duration,
                'operation': fac.AntdSpace([
                    fac.AntdButton(
                        '详情',
                        id={'type': 'task-detail-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-eye')
                    ),
                    fac.AntdButton(
                        '日志',
                        id={'type': 'task-log-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-file-text')
                    ),
                    fac.AntdButton(
                        '取消' if current_status in ['pending', 'running'] else '重启',
                        id={'type': 'task-action-btn', 'index': i},
                        type='link',
                        size='small',
                        danger=current_status in ['pending', 'running'],
                        icon=fac.AntdIcon(
                            icon='antd-stop' if current_status in ['pending', 'running'] else 'antd-redo'
                        ),
                        disabled=current_status == 'running'
                    )
                ])
            })

        # 模拟统计数据
        total_count = 500
        running_count = 15
        pending_count = 25
        completed_count = 400

        # 更新分页信息
        new_pagination = {
            'current': page,
            'pageSize': page_size,
            'total': total_count,
            'showSizeChanger': True,
            'showQuickJumper': True,
            'total': 0,
            'pageSizeOptions': ['10', '20', '50'],
            'showLessItems': True
        }

        return (
            mock_data,
            new_pagination,
            False,  # 停止加载
            total_count,
            running_count,
            pending_count,
            completed_count,
            current_params,
            None
        )

    except Exception as e:
        return (
            [],
            {'current': 1, 'pageSize': 20, 'total': 0},
            False,
            0, 0, 0, 0,
            {},
            fac.AntdMessage(content=f'查询失败: {str(e)}', type='error')
        )


# 重置查询条件回调
@app.callback(
    [Output('task-name-input', 'value'),
     Output('task-type-select', 'value'),
     Output('task-status-select', 'value'),
     Output('task-date-range', 'value')],
    [Input('task-reset-btn', 'n_clicks')],
    prevent_initial_call=True
)
def reset_task_search_form(n_clicks):
    """
    重置任务查询表单
    """
    if n_clicks:
        return '', '', '', None
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update


# 控制自动刷新定时器
@app.callback(
    Output('task-monitor-interval', 'disabled'),
    [Input('task-auto-refresh-switch', 'checked')],
    prevent_initial_call=True
)
def control_auto_refresh_timer(auto_refresh_enabled):
    """
    控制自动刷新定时器的启用/禁用
    """
    return not auto_refresh_enabled


# 批量操作按钮状态控制
@app.callback(
    [Output('task-batch-cancel-btn', 'disabled'),
     Output('task-batch-restart-btn', 'disabled')],
    [Input('task-monitor-table', 'selectedRowKeys')],
    prevent_initial_call=True
)
def update_batch_task_buttons(selected_keys):
    """
    根据选择的任务更新批量操作按钮状态
    """
    has_selection = selected_keys and len(selected_keys) > 0
    return not has_selection, not has_selection
