"""
设备管理页面
支持设备连接状态监控、设备配置、性能监控等功能
"""
import dash
from dash import html, dcc, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_antd_charts as fact
import feffery_utils_components as fuc
from datetime import datetime
import json

from api.phone_data import (
    get_device_list_api,
    get_device_status_api,
    connect_device_api,
    disconnect_device_api
)


def render_device_management(button_perms=None, role_perms=None):
    """
    渲染设备管理页面
    """
    return html.Div([
        # 页面标题
        fac.AntdPageHeader(
            title='设备管理',
            subTitle='管理和监控所有连接的设备状态',
            ghost=False
        ),
        
        # 设备状态概览
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='在线设备',
                        value=0,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-check-circle'),
                        id='online-devices-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='离线设备',
                        value=0,
                        valueStyle={'color': '#faad14'},
                        prefix=fac.AntdIcon(icon='antd-exclamation-circle'),
                        id='offline-devices-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='错误设备',
                        value=0,
                        valueStyle={'color': '#ff4d4f'},
                        prefix=fac.AntdIcon(icon='antd-close-circle'),
                        id='error-devices-stat'
                    )
                ], hoverable=True)
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总设备数',
                        value=0,
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-mobile'),
                        id='total-devices-stat'
                    )
                ], hoverable=True)
            ], span=6)
        ], gutter=16, style={'marginBottom': 24}),
        
        # 设备操作区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '刷新设备',
                            id='refresh-devices-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-reload')
                        ),
                        fac.AntdButton(
                            '添加设备',
                            id='add-device-btn',
                            icon=fac.AntdIcon(icon='antd-plus')
                        ),
                        fac.AntdButton(
                            '批量连接',
                            id='batch-connect-btn',
                            icon=fac.AntdIcon(icon='antd-link')
                        ),
                        fac.AntdButton(
                            '批量断开',
                            id='batch-disconnect-btn',
                            icon=fac.AntdIcon(icon='antd-disconnect'),
                            danger=True
                        )
                    ])
                ], span=16),
                
                fac.AntdCol([
                    fac.AntdInput(
                        id='device-search-input',
                        placeholder='搜索设备名称或ID',
                        suffix=fac.AntdIcon(icon='antd-search'),
                        style={'width': '100%'}
                    )
                ], span=8)
            ], style={'marginBottom': 16}),
            
            # 设备列表表格
            fac.AntdTable(
                id='devices-table',
                columns=[
                    {
                        'title': '',
                        'dataIndex': 'selection',
                        'width': 50,
                        'renderOptions': {'renderType': 'checkbox'}
                    },
                    {'title': '设备名称', 'dataIndex': 'device_name', 'width': 150},
                    {'title': '设备ID', 'dataIndex': 'device_id', 'width': 120},
                    {'title': '设备类型', 'dataIndex': 'device_type', 'width': 100},
                    {'title': '设备型号', 'dataIndex': 'device_model', 'width': 150},
                    {'title': '系统版本', 'dataIndex': 'system_version', 'width': 120},
                    {'title': '连接状态', 'dataIndex': 'status', 'width': 100},
                    {'title': 'IP地址', 'dataIndex': 'ip_address', 'width': 120},
                    {'title': '最后心跳', 'dataIndex': 'last_heartbeat', 'width': 150},
                    {'title': '性能指标', 'dataIndex': 'performance', 'width': 200},
                    {'title': '操作', 'dataIndex': 'operation', 'width': 200}
                ],
                data=[],
                pagination={
                    'pageSize': 10,
                    'current': 1,
                    'showSizeChanger': True,
                    'pageSizeOptions': [10, 20, 50, 100],
                    'showQuickJumper': True,
                    'total': 0
                },
                rowSelectionType='checkbox',
                selectedRowKeys=[],
                bordered=True,
                size='small'
            )
        ], title='设备列表'),
        
        # 设备详情模态框
        fac.AntdModal(
            id='device-detail-modal',
            title='设备详情',
            visible=False,
            width=800,
            children=[
                html.Div(id='device-detail-content')
            ]
        ),
        
        # 添加设备模态框
        fac.AntdModal(
            id='add-device-modal',
            title='添加设备',
            visible=False,
            width=600,
            children=[
                fac.AntdForm([
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='new-device-name',
                            placeholder='请输入设备名称'
                        )
                    ], label='设备名称', required=True),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='new-device-id',
                            placeholder='请输入设备唯一标识'
                        )
                    ], label='设备ID', required=True),
                    
                    fac.AntdFormItem([
                        fac.AntdSelect(
                            id='new-device-type',
                            options=[
                                {'label': 'Android', 'value': 'android'},
                                {'label': 'iOS', 'value': 'ios'}
                            ],
                            placeholder='请选择设备类型'
                        )
                    ], label='设备类型', required=True),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='new-device-model',
                            placeholder='请输入设备型号'
                        )
                    ], label='设备型号'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='new-device-ip',
                            placeholder='请输入IP地址'
                        )
                    ], label='IP地址'),
                    
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='new-device-config',
                            placeholder='请输入设备配置（JSON格式）',
                            mode='text-area',
                            autoSize={'minRows': 3, 'maxRows': 6}
                        )
                    ], label='设备配置')
                ], layout='vertical')
            ],
            okText='添加',
            cancelText='取消'
        ),
        
        # 设备性能监控图表
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='设备状态分布'),
                    html.Div(id='device-status-chart', style={'height': 300})
                ], title='状态统计')
            ], span=12),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdCardMeta(title='设备性能趋势'),
                    html.Div(id='device-performance-chart', style={'height': 300})
                ], title='性能监控')
            ], span=12)
        ], gutter=16, style={'marginTop': 24}),
        
        # 消息提示容器
        html.Div(id='device-message-container'),
        
        # 定时刷新
        dcc.Interval(
            id='device-interval',
            interval=10*1000,  # 10秒刷新一次
            n_intervals=0
        )
    ])


# 刷新设备列表回调
@callback(
    [Output('devices-table', 'data'),
     Output('online-devices-stat', 'value'),
     Output('offline-devices-stat', 'value'),
     Output('error-devices-stat', 'value'),
     Output('total-devices-stat', 'value'),
     Output('device-status-chart', 'children'),
     Output('device-performance-chart', 'children')],
    [Input('refresh-devices-btn', 'n_clicks'),
     Input('device-interval', 'n_intervals'),
     Input('device-search-input', 'value')],
    prevent_initial_call=True
)
def refresh_device_list(refresh_clicks, n_intervals, search_value):
    """
    刷新设备列表和统计数据
    """
    try:
        # 构建查询参数
        query_params = {}
        if search_value:
            query_params['search'] = search_value
        
        response = get_device_list_api({'page': 1, 'page_size': 100}, query_params)
        
        if response and response.get('code') == 200:
            devices = response.get('data', {}).get('rows', [])
            
            # 统计设备状态
            online_count = sum(1 for d in devices if d.get('status') == 'online')
            offline_count = sum(1 for d in devices if d.get('status') == 'offline')
            error_count = sum(1 for d in devices if d.get('status') == 'error')
            total_count = len(devices)
            
            # 格式化设备数据
            formatted_devices = []
            for device in devices:
                # 格式化状态
                status = device.get('status', 'offline')
                status_color = {
                    'online': 'success',
                    'offline': 'warning',
                    'error': 'error'
                }.get(status, 'default')
                
                status_component = fac.AntdTag(
                    content=status.upper(),
                    color=status_color,
                    icon=fac.AntdIcon(icon={
                        'online': 'antd-check-circle',
                        'offline': 'antd-exclamation-circle',
                        'error': 'antd-close-circle'
                    }.get(status, 'antd-question-circle'))
                )
                
                # 格式化性能指标
                performance_info = device.get('performance_info', {})
                if isinstance(performance_info, str):
                    try:
                        performance_info = json.loads(performance_info)
                    except:
                        performance_info = {}
                
                cpu_usage = performance_info.get('cpu_usage', 0)
                memory_usage = performance_info.get('memory_usage', 0)
                battery = performance_info.get('battery', 0)
                
                performance_component = html.Div([
                    fac.AntdProgress(
                        percent=cpu_usage,
                        size='small',
                        format={'content': f'CPU: {cpu_usage}%'},
                        strokeColor='#1890ff'
                    ),
                    fac.AntdProgress(
                        percent=memory_usage,
                        size='small',
                        format={'content': f'内存: {memory_usage}%'},
                        strokeColor='#52c41a',
                        style={'marginTop': 4}
                    ),
                    fac.AntdProgress(
                        percent=battery,
                        size='small',
                        format={'content': f'电量: {battery}%'},
                        strokeColor='#faad14',
                        style={'marginTop': 4}
                    )
                ])
                
                # 操作按钮
                operation_buttons = fac.AntdSpace([
                    fac.AntdButton(
                        '详情',
                        id={'type': 'device-detail-btn', 'index': device.get('id')},
                        type='link',
                        size='small'
                    ),
                    fac.AntdButton(
                        '连接' if status != 'online' else '断开',
                        id={'type': 'toggle-device-btn', 'index': device.get('id')},
                        type='link',
                        size='small',
                        danger=status == 'online'
                    ),
                    fac.AntdButton(
                        '配置',
                        id={'type': 'config-device-btn', 'index': device.get('id')},
                        type='link',
                        size='small'
                    )
                ])
                
                formatted_devices.append({
                    'key': str(device.get('id')),
                    'device_name': device.get('device_name', ''),
                    'device_id': device.get('device_id', ''),
                    'device_type': device.get('device_type', '').upper(),
                    'device_model': device.get('device_model', ''),
                    'system_version': device.get('system_version', ''),
                    'status': status_component,
                    'ip_address': device.get('ip_address', ''),
                    'last_heartbeat': device.get('last_heartbeat', ''),
                    'performance': performance_component,
                    'operation': operation_buttons
                })
            
            # 状态分布图表
            status_data = [
                {'status': '在线', 'count': online_count, 'color': '#52c41a'},
                {'status': '离线', 'count': offline_count, 'color': '#faad14'},
                {'status': '错误', 'count': error_count, 'color': '#ff4d4f'}
            ]
            
            status_chart = fact.AntdPie(
                data=status_data,
                angleField='count',
                colorField='status',
                radius=0.8,
                label={'type': 'outer', 'content': '{name}: {value}'},
                color=['#52c41a', '#faad14', '#ff4d4f']
            )
            
            # 性能趋势图表（模拟数据）
            performance_data = [
                {'time': '10:00', 'cpu': 45, 'memory': 60},
                {'time': '10:30', 'cpu': 52, 'memory': 65},
                {'time': '11:00', 'cpu': 48, 'memory': 62},
                {'time': '11:30', 'cpu': 55, 'memory': 68},
                {'time': '12:00', 'cpu': 42, 'memory': 58}
            ]
            
            performance_chart = fact.AntdLine(
                data=performance_data,
                xField='time',
                yField='cpu',
                seriesField='type',
                smooth=True,
                color=['#1890ff', '#52c41a']
            )
            
            return (
                formatted_devices,
                online_count,
                offline_count,
                error_count,
                total_count,
                status_chart,
                performance_chart
            )
        else:
            return [], 0, 0, 0, 0, html.Div(), html.Div()
            
    except Exception as e:
        return [], 0, 0, 0, 0, html.Div(), html.Div()


# 显示添加设备模态框
@callback(
    Output('add-device-modal', 'visible'),
    [Input('add-device-btn', 'n_clicks')],
    prevent_initial_call=True
)
def show_add_device_modal(n_clicks):
    """
    显示添加设备模态框
    """
    if n_clicks:
        return True
    return dash.no_update
