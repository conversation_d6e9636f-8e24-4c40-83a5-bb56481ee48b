"""
任务历史页面 - 查看历史任务执行记录
"""
import dash
from server import appfrom dash import dcc, html, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, date, timedelta
import json


def render_task_history(button_perms=None, role_perms=None):
    """
    渲染任务历史页面
    """
    return [
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdBreadcrumb(
                    items=[
                        {'title': '电话标记系统'},
                        {'title': '任务历史'}
                    ]
                )
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 查询表单
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdForm([
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdInput(
                                        id='task-history-task-name-input',
                                        placeholder='请输入任务名称',
                                        allowClear=True
                                    )
                                ], label='任务名称')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='task-history-task-type-select',
                                        placeholder='请选择任务类型',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '批量导入', 'value': 'batch_import'},
                                            {'label': '批量处理', 'value': 'batch_process'},
                                            {'label': '数据导出', 'value': 'data_export'},
                                            {'label': '数据同步', 'value': 'data_sync'},
                                            {'label': '系统维护', 'value': 'system_maintenance'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='任务类型')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSelect(
                                        id='task-history-status-select',
                                        placeholder='请选择执行状态',
                                        options=[
                                            {'label': '全部', 'value': ''},
                                            {'label': '成功', 'value': 'success'},
                                            {'label': '失败', 'value': 'failed'},
                                            {'label': '部分成功', 'value': 'partial_success'},
                                            {'label': '已取消', 'value': 'cancelled'}
                                        ],
                                        value='',
                                        allowClear=True
                                    )
                                ], label='执行状态')
                            ], span=6),
                            
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdDatePicker(
                                        id='task-history-date-range',
                                        placeholder='选择执行日期'
                                    )
                                ], label='执行时间')
                            ], span=6)
                        ], gutter=16),
                        
                        fac.AntdRow([
                            fac.AntdCol([
                                fac.AntdFormItem([
                                    fac.AntdSpace([
                                        fac.AntdButton(
                                            '查询',
                                            id='task-history-search-btn',
                                            type='primary',
                                            icon=fac.AntdIcon(icon='antd-search')
                                        ),
                                        fac.AntdButton(
                                            '重置',
                                            id='task-history-reset-btn',
                                            icon=fac.AntdIcon(icon='antd-sync')
                                        ),
                                        fac.AntdButton(
                                            '导出记录',
                                            id='task-history-export-btn',
                                            icon=fac.AntdIcon(icon='antd-download')
                                        )
                                    ])
                                ])
                            ], span=24)
                        ])
                    ], layout='inline')
                ], size='small')
            ], span=24)
        ], style={'marginBottom': 16}),
        
        # 统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总任务数',
                        value=1256,
                        valueStyle={'color': '#3f8600'},
                        prefix=fac.AntdIcon(icon='antd-file-text')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='成功任务',
                        value=1089,
                        valueStyle={'color': '#52c41a'},
                        prefix=fac.AntdIcon(icon='antd-check-circle')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='失败任务',
                        value=89,
                        valueStyle={'color': '#f5222d'},
                        prefix=fac.AntdIcon(icon='antd-close-circle')
                    )
                ])
            ], span=6),
            
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='成功率',
                        value=86.7,
                        precision=1,
                        suffix='%',
                        valueStyle={'color': '#1890ff'},
                        prefix=fac.AntdIcon(icon='antd-rise')
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': 16}),
        
        # 任务历史表格
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTable(
                        id='task-history-table',
                        columns=[
                            {'title': '任务ID', 'dataIndex': 'task_id', 'width': 120},
                            {'title': '任务名称', 'dataIndex': 'task_name', 'width': 200},
                            {'title': '任务类型', 'dataIndex': 'task_type', 'width': 120},
                            {'title': '执行用户', 'dataIndex': 'executor', 'width': 100},
                            {'title': '开始时间', 'dataIndex': 'start_time', 'width': 150},
                            {'title': '结束时间', 'dataIndex': 'end_time', 'width': 150},
                            {'title': '执行时长', 'dataIndex': 'duration', 'width': 100},
                            {'title': '处理数量', 'dataIndex': 'processed_count', 'width': 100},
                            {'title': '成功数量', 'dataIndex': 'success_count', 'width': 100},
                            {'title': '执行状态', 'dataIndex': 'status', 'width': 100},
                            {'title': '操作', 'dataIndex': 'operation', 'width': 200, 'fixed': 'right'}
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'total': 0
                        },
                        bordered=True,
                        size='small',
                        maxWidth=1400
                    )
                ])
            ], span=24)
        ]),
        
        # 任务详情模态框
        fac.AntdModal(
            id='task-detail-modal',
            title='任务详情',
            visible=False,
            width=800,
            children=[
                fac.AntdDescriptions(
                    id='task-detail-descriptions',
                    items=[],
                    bordered=True,
                    column=2
                ),
                html.Div([
                    fac.AntdTitle('执行日志', level=4, style={'marginTop': 16}),
                    fac.AntdInput(
                        id='task-detail-log',
                        mode='text-area',
                        autoSize={'minRows': 10, 'maxRows': 10},
                        readOnly=True,
                        style={'fontFamily': 'monospace'}
                    )
                ])
            ],
            okText='关闭',
            cancelText=None
        ),
        
        # 消息提示容器
        html.Div(id='task-history-message-container'),
        
        # 存储组件
        dcc.Store(id='task-history-current-page', data=1),
        dcc.Store(id='task-history-page-size', data=20)
    ]


# 查询任务历史回调
@app.callback(
    [Output('task-history-table', 'data'),
     Output('task-history-table', 'pagination'),
     Output('task-history-message-container', 'children')],
    [Input('task-history-search-btn', 'n_clicks'),
     Input('task-history-table', 'pagination')],
    [State('task-history-task-name-input', 'value'),
     State('task-history-task-type-select', 'value'),
     State('task-history-status-select', 'value'),
     State('task-history-date-range', 'value')],
    prevent_initial_call=False
)
def search_task_history(search_clicks, pagination, task_name, task_type, status, date_range):
    """
    查询任务历史数据
    """
    try:
        # 分页参数
        page = pagination.get('current', 1) if pagination else 1
        page_size = pagination.get('pageSize', 20) if pagination else 20
        
        # 模拟任务历史数据
        mock_data = []
        start_index = (page - 1) * page_size + 1
        
        task_types = {
            'batch_import': '批量导入',
            'batch_process': '批量处理', 
            'data_export': '数据导出',
            'data_sync': '数据同步',
            'system_maintenance': '系统维护'
        }
        
        status_map = {
            'success': {'text': '成功', 'color': 'success'},
            'failed': {'text': '失败', 'color': 'error'},
            'partial_success': {'text': '部分成功', 'color': 'warning'},
            'cancelled': {'text': '已取消', 'color': 'default'}
        }
        
        for i in range(start_index, start_index + page_size):
            task_type_key = list(task_types.keys())[i % len(task_types)]
            status_key = list(status_map.keys())[i % len(status_map)]
            
            processed_count = 1000 + i * 50
            success_count = int(processed_count * (0.9 if status_key == 'success' else 0.7))
            
            mock_data.append({
                'key': str(i),
                'task_id': f'TASK_{i:06d}',
                'task_name': f'{task_types[task_type_key]}任务_{i}',
                'task_type': fac.AntdTag(
                    content=task_types[task_type_key],
                    color='blue'
                ),
                'executor': 'admin',
                'start_time': f'2025-07-{(i % 30) + 1:02d} 09:00:00',
                'end_time': f'2025-07-{(i % 30) + 1:02d} 09:{30 + (i % 30):02d}:00',
                'duration': f'{30 + (i % 30)}分钟',
                'processed_count': processed_count,
                'success_count': success_count,
                'status': fac.AntdTag(
                    content=status_map[status_key]['text'],
                    color=status_map[status_key]['color']
                ),
                'operation': fac.AntdSpace([
                    fac.AntdButton(
                        '详情',
                        id={'type': 'task-detail-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-eye')
                    ),
                    fac.AntdButton(
                        '重新执行',
                        id={'type': 'task-rerun-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-redo'),
                        disabled=status_key == 'success'
                    ),
                    fac.AntdButton(
                        '下载日志',
                        id={'type': 'task-log-download-btn', 'index': i},
                        type='link',
                        size='small',
                        icon=fac.AntdIcon(icon='antd-download')
                    )
                ])
            })
        
        # 更新分页信息
        total_count = 1256  # 模拟总数
        new_pagination = {
            'current': page,
            'pageSize': page_size,
            'total': total_count,
            'showSizeChanger': True,
            'showQuickJumper': True,
            'total': 0,
            'pageSizeOptions': ['10', '20', '50', '100']
        }
        
        return mock_data, new_pagination, None
        
    except Exception as e:
        return [], {'current': 1, 'pageSize': 20, 'total': 0}, fac.AntdMessage(
            content=f'查询失败: {str(e)}', type='error'
        )


# 重置查询条件回调
@app.callback(
    [Output('task-history-task-name-input', 'value'),
     Output('task-history-task-type-select', 'value'),
     Output('task-history-status-select', 'value'),
     Output('task-history-date-range', 'value')],
    [Input('task-history-reset-btn', 'n_clicks')],
    prevent_initial_call=True
)
def reset_search_form(n_clicks):
    """
    重置查询表单
    """
    if n_clicks:
        return '', '', '', None
    return dash.no_update, dash.no_update, dash.no_update, dash.no_update


# 任务详情回调
@app.callback(
    [Output('task-detail-modal', 'visible'),
     Output('task-detail-descriptions', 'items'),
     Output('task-detail-log', 'value')],
    [Input({'type': 'task-detail-btn', 'index': dash.dependencies.ALL}, 'n_clicks')],
    prevent_initial_call=True
)
def show_task_detail(detail_clicks):
    """
    显示任务详情
    """
    if any(detail_clicks):
        # 模拟任务详情数据
        detail_items = [
            {'label': '任务ID', 'children': 'TASK_000123'},
            {'label': '任务名称', 'children': '批量导入任务_123'},
            {'label': '任务类型', 'children': '批量导入'},
            {'label': '执行用户', 'children': 'admin'},
            {'label': '开始时间', 'children': '2025-07-01 09:00:00'},
            {'label': '结束时间', 'children': '2025-07-01 09:30:00'},
            {'label': '执行时长', 'children': '30分钟'},
            {'label': '处理数量', 'children': '1500'},
            {'label': '成功数量', 'children': '1350'},
            {'label': '失败数量', 'children': '150'},
            {'label': '执行状态', 'children': '部分成功'},
            {'label': '错误信息', 'children': '部分号码格式不正确'}
        ]
        
        log_content = """2025-07-01 09:00:00 [INFO] 任务开始执行
2025-07-01 09:00:01 [INFO] 读取导入文件: phone_numbers.xlsx
2025-07-01 09:00:02 [INFO] 文件包含 1500 条记录
2025-07-01 09:00:03 [INFO] 开始数据验证...
2025-07-01 09:05:00 [INFO] 数据验证完成，有效记录: 1350
2025-07-01 09:05:01 [WARN] 发现 150 条无效记录
2025-07-01 09:05:02 [INFO] 开始批量处理...
2025-07-01 09:25:00 [INFO] 批量处理完成
2025-07-01 09:25:01 [INFO] 成功处理: 1350 条
2025-07-01 09:25:02 [ERROR] 处理失败: 150 条
2025-07-01 09:30:00 [INFO] 任务执行完成"""
        
        return True, detail_items, log_content
    
    return False, [], ''
